﻿namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class ProductSearchDTO
{
    public string Key { get; set; }
    public string Name { get; set; }
    public bool IsFreePrice { get; set; }
    public List<ProductVariantDTO> Variants { get; set; }

    public ProductSearchDTO(string key, string name, bool isFreePrice, List<ProductVariantDTO> variants)
    {
        Key = key;
        Name = name;
        IsFreePrice = isFreePrice;
        Variants = variants;
    }
}
