# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# N. Rey 22/06/2020

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/IT.Microservices.CT.Order.Wrapper.csproj", "/src/IT.Microservices.CT.Order.Wrapper/"]
COPY ["src/NuGet.config", "/src/IT.Microservices.CT.Order.Wrapper/"]

RUN dotnet restore "IT.Microservices.CT.Order.Wrapper/IT.Microservices.CT.Order.Wrapper.csproj"

WORKDIR "/src/IT.Microservices.CT.Order.Wrapper"

COPY . .

RUN dotnet build "src/IT.Microservices.CT.Order.Wrapper.csproj" -c Release
RUN dotnet test "tests/IT.Microservices.CT.Order.Wrapper.UnitTests/IT.Microservices.CT.Order.Wrapper.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/IT.Microservices.CT.Order.Wrapper.csproj" -c Release -o out

ENTRYPOINT sleep 10000