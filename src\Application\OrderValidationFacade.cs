﻿using FluentValidation.Results;
using IT.Microservices.CT.Order.Wrapper.Domain.Validator;
using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.Settings;
using ITF.Order.Library.Application;
using ITF.SharedLibraries.Availability;
using ITF.SharedLibraries.Availability.dto;
using ITF.SharedModels.DataModels.Globals;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using JasperFx.Core;
using Marten;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net;

namespace IT.Microservices.CT.Order.Wrapper.Application;

public interface IOrderValidationFacade
{
    Task<ValidationResponse> ValidateOrder(GlobalOrderModel order);
}

public class OrderValidationFacade(IAvailabilityHttpService availabilityHttpService,
    IOptionsMonitor<CTOrderWrapperSettings> ctOrderWrapperSettings,
    IOptionsMonitor<CommerceToolCustomSettings> commerceToolsCommonSettings,
    ICTProductSearchService cTProductSearchService,
    IFloristRepository floristRepository) : IOrderValidationFacade
{

    // TODO : if PSM => check if a desciption is provided
    // TODO : check ceremony delay

    public async Task<ValidationResponse> ValidateOrder(GlobalOrderModel order)
    {
        var validator = new GlobalOrderValidator(commerceToolsCommonSettings);
        ValidationResult results = validator.Validate(order);
        if (!results.IsValid)
        {
            string errorMessage = string.Empty;
            results.Errors.ForEach(error => errorMessage += String.Join(", ", error.ErrorMessage));
            return new ValidationResponse {
                IsValide = false,
                ErrorMessage = errorMessage,
                ErrorType = ErrorMessageTypes.VALIDATION_EXCEPTION
            };
        }

        if (ctOrderWrapperSettings?.CurrentValue?.CountryCode.Equals("FR", StringComparison.OrdinalIgnoreCase) ?? false)
        {
            var response = await CheckFrenchOrder(order);
            if (!response.IsValide)
            {
                return response;
            }
        }

        return new ValidationResponse
        {
            IsValide = true,
            ErrorMessage = string.Empty,
            ErrorType = string.Empty
        };
    }

    private async Task<ValidationResponse> CheckFrenchOrder(GlobalOrderModel order)
    {
        if (order.SenderFloristIdentifier == order.ExecutingFloristIdentifier)
        {
            return new ValidationResponse
            {
                IsValide = false,
                ErrorMessage = "Sender and executing florist cannot be the same",
                ErrorType = ErrorMessageTypes.VALIDATION_EXCEPTION
            };
        }

        foreach (GlobalOrderProduct product in order.Products)
        {
            bool flowControl = false;
            ValidationResponse value = new();

            (flowControl, value) = OrderValidationUtil.CheckProduct(product, order.Shipping.DeliveryDate, order.Shipping.Moment);
            if (!flowControl)
            {
                return value;
            }

            var ctProduct = await cTProductSearchService.GetProductByKey(product.ProductKey);
            if (ctProduct == null)
            {
                return new ValidationResponse
                {
                    IsValide = false,
                    ErrorMessage = $"Product with key {product.ProductKey} not found",
                    ErrorType = ErrorMessageTypes.VALIDATION_EXCEPTION
                };
            }

            var frenchSettings = ctOrderWrapperSettings.CurrentValue.FrenchSettings;
            var preparationTime = CommerceToolsHelper.GetLongAttribute(CtProductCustomAttributesNames.VariantAttributes.FLORIST_PREPARATION_TIME, ctProduct.MasterVariant.Attributes);

            if (order.Shipping.Moment.Equals(MomentEnum.Morning))
            {
                (flowControl, value) = OrderValidationUtil.CheckDeliveryDate(order.Shipping.DeliveryDate, frenchSettings.MorningLimitHour, frenchSettings.MorningLimitMinutes, preparationTime);
            }
            else
            {
                (flowControl, value) = OrderValidationUtil.CheckDeliveryDate(order.Shipping.DeliveryDate, frenchSettings.AfternoonLimitHour, frenchSettings.AfternoonLimitMinutes, preparationTime);
            }

            if (!flowControl)
            {
                return value;
            }

            (flowControl, value) = OrderValidationUtil.CheckProductPrice(product, frenchSettings.ProductMinPrice, frenchSettings.ProductMaxPrice);
            if (!flowControl)
            {
                return value;
            }

            // TODO get min and max prices from variants and check order price
            //var crPrice = GetCtVariantPrice(product.VariantKey, ctProduct, productQuantity);

        }
        var address = String.Empty;
        if(!order.Shipping.StreetNumber.IsEmpty())
        {             
            address = order.Shipping.StreetNumber + " ";
        }
        address += order.Shipping.StreetName;

        var dto = new AvailabilityInputDto()
        {
            ProductSKU = order.Products.Select(p => p.ProductKey).FirstOrDefault(),
            ProductSKUs = order.Products.Select(p => p.ProductKey).ToList(),
            CountryCode = order.DeliveryCountryCode,
            DeliveryDate = order.Shipping.DeliveryDate,
            City = order.Shipping.City,
            Street = address,
            Moment = OrderValidationUtil.ConvertMomentToDeliveryMoment(order.Shipping.Moment),
            OrderId = order.OrderNumber,
            PostalCode = order.Shipping.ZipCode
        };

        var availabilityResponse = await availabilityHttpService.CheckAvailability(dto);
        if (availabilityResponse.StatusCode == HttpStatusCode.InternalServerError)
        {
            string errorResponseData = await availabilityResponse.Content.ReadAsStringAsync();
            var errorResponse = JsonConvert.DeserializeObject<ErrorObjectReponse>(errorResponseData);

            return new ValidationResponse
            {
                IsValide = false,
                ErrorMessage = errorResponse != null ? errorResponse.Error : "",
                ErrorType = ErrorMessageTypes.FEASABILITY_EXCEPTION_RAO_KO
            };
        }

        string responseData = await availabilityResponse.Content.ReadAsStringAsync();
        var feasabilityResponse = JsonConvert.DeserializeObject<AvailabilityOutputDTO>(responseData);
        if (feasabilityResponse == null)
        {
            return new ValidationResponse
            {
                IsValide = false,
                ErrorMessage = "Response from RAO is null or invalid",
                ErrorType = ErrorMessageTypes.FEASABILITY_EXCEPTION_RAO_RESPONSE_DATA_KO
            };
        }

        if (!OrderValidationUtil.IsOrderIsFeasible(feasabilityResponse))
        {
            if (order.CodeAP.IsEmpty())
            {
                return new ValidationResponse
                {
                    IsValide = false,
                    ErrorMessage = "No florists in availability response",
                    ErrorType = ErrorMessageTypes.FEASABILITY_EXCEPTION_EMPTY_FLORISTS
                };
            }

            var codeApFromDBValid = false;
            var executingFlorist = await floristRepository.GetById(order.ExecutingFloristIdentifier);
            (codeApFromDBValid, var response) = OrderValidationUtil.ValidatePersonalCodeFromDb(order.CodeAP, executingFlorist);

            if (!codeApFromDBValid) // code AP not found
            {
                return response;
            }
        }
        else
        {
            // the order is feasible but we have a code AP
            // we use the response from RAO to validate the code AP
            if (!order.CodeAP.IsEmpty())
            {
                var codeApValid = false;

                foreach(FloristDistanceDto florist in feasabilityResponse.ExecutingFlorists)
                {
                    var floristModel = await floristRepository.GetById(florist.FloristId);
                    if (floristModel != null && floristModel.PersonalCode.EqualsIgnoreCase(order.CodeAP))
                    {
                        codeApValid = true;
                        break;
                    }
                }

                if (!codeApValid)
                {
                    return new ValidationResponse
                    {
                        IsValide = false,
                        ErrorMessage = "Personal code not found in florists list",
                        ErrorType = ErrorMessageTypes.PERSONAL_CODE_EXCEPTION_PERSONAL_CODE_NOT_FOUND
                    };
                }
            }
            else
            {
                // if a executing florist is specified, we check if it exists in the RAO response
                if (!order.ExecutingFloristIdentifier.IsEmpty())
                {
                    var executingFloristFound = false;
                    foreach (FloristDistanceDto florist in feasabilityResponse.ExecutingFlorists)
                    {
                        if (florist.FloristId.EqualsIgnoreCase(order.ExecutingFloristIdentifier))
                        {
                            executingFloristFound = true;
                            break;
                        }
                    }
                    if (!executingFloristFound)
                    {
                        return new ValidationResponse
                        {
                            IsValide = false,
                            ErrorMessage = "Executing florist not found in RAO response",
                            ErrorType = ErrorMessageTypes.SELECTED_EXECUTING_FLORIST_NOT_FOUND
                        };
                    }
                }
            }
        }

        return new ValidationResponse
        {
            IsValide = true,
            ErrorMessage = string.Empty,
            ErrorType = string.Empty
        };

    }
}
