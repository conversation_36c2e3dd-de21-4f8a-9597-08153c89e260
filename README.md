# IT.Microservices.CT.Order.Wrapper

Write a description here ...

## Docker compose

Describe the docker compose 

```dockerfile
  itctorderwrapper:
    image: ${DOCKER_REGISTRY-}itctorderwrapper
    container_name: itctorderwrapper
    build:
      context: .
      dockerfile: src/IT.Microservices.CT.Order.Wrapper/src/Dockerfile
```

```dockerfile
  itctorderwrapper:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - UNLEASH__KEY=*:default.0f1cc0c83ac07695d565dbc4317af531d1d9dd3e864f52137da6112a
    ports:
      - "99999:80"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
```
## Settings

Indicate here the list of specific settings here, and their purpose

```json
  "SettingExample": {
    "Setting1": "http://someurl:9999",
    "Enabled": true,
    "SubParameter1": "none"
  }
```

## Usage / examples

Indicate here some examples of how to use it ...

```bash
# Perform a call and return something ...
curl --location --request GET 'http://localhost:99999/api/Any/Something?id=1234' \
--header 'Content-Type: application/json' 
```