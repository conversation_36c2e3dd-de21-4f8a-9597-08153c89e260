using ITF.Lib.Common.DomainDrivenDesign;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.MongoDB.Repository
{
    // https://alexalvess.medium.com/getting-started-with-net-core-api-mongodb-and-transactions-c7a021684d01
    public abstract class MongoRepository<T> : IMongoRepository<T> where T : BaseClass<string>
    {
        private readonly IMongoClient _mongoClient;
        private readonly string _collection;
        private readonly string _databaseName;
        public IMongoCollection<T> Collection { get; }
        // Existing constructor for backward compatibility
        public MongoRepository(IMongoClient mongoClient, string databaseName , string collection)
        {
            if (string.IsNullOrWhiteSpace(collection))
                throw new ArgumentNullException(nameof(collection));

            if (string.IsNullOrWhiteSpace(databaseName))
                throw new ArgumentNullException(nameof(databaseName));

            (_mongoClient, _databaseName, _collection) = (mongoClient, databaseName, collection);

            InitializeCollection();
            Collection = _mongoClient.GetDatabase(_databaseName).GetCollection<T>(_collection);
        }

        // New constructor with Configuration support for TTL
        public MongoRepository(IMongoClient mongoClient, ITF.SharedLibraries.MongoDB.Configuration configuration, string collection)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            if (string.IsNullOrWhiteSpace(collection))
                throw new ArgumentNullException(nameof(collection));

            if (string.IsNullOrWhiteSpace(configuration.DatabaseName))
                throw new ArgumentNullException(nameof(configuration.DatabaseName));

            (_mongoClient, _databaseName, _collection) = (mongoClient, configuration.DatabaseName, collection);

            var isNewCollection = InitializeCollection();
            Collection = _mongoClient.GetDatabase(_databaseName).GetCollection<T>(_collection);

            // Handle TTL index creation if configured
            if (configuration.TtlExpirationSeconds.HasValue && !string.IsNullOrWhiteSpace(configuration.TtlFieldName))
            {
                try
                {
                    HandleTtlIndexCreation(configuration.TtlFieldName, configuration.TtlExpirationSeconds.Value,
                        configuration.ForceTtlIndexActivation, isNewCollection);
                }
                catch (Exception e)
                {
                    Log.Logger.Error(e, "Failed to create or update TTL index on collection {collection} for field {field}",
                        _collection, configuration.TtlFieldName);
                }
            }
        }

        // Constructor with explicit TTL parameters (alternative approach)
        public MongoRepository(IMongoClient mongoClient, string databaseName, string collection,
            string ttlFieldName, int ttlExpirationSeconds, bool forceTtlActivation = false)
        {
            if (string.IsNullOrWhiteSpace(collection))
                throw new ArgumentNullException(nameof(collection));

            if (string.IsNullOrWhiteSpace(databaseName))
                throw new ArgumentNullException(nameof(databaseName));

            (_mongoClient, _databaseName, _collection) = (mongoClient, databaseName, collection);

            var isNewCollection = InitializeCollection();
            Collection = _mongoClient.GetDatabase(_databaseName).GetCollection<T>(_collection);

            // Handle TTL index creation
            if (!string.IsNullOrWhiteSpace(ttlFieldName) && ttlExpirationSeconds > 0)
            {
                try
                {
                    HandleTtlIndexCreation(ttlFieldName, ttlExpirationSeconds, forceTtlActivation, isNewCollection);
                }
                catch (Exception e)
                {
                    Log.Logger.Error(e, "Failed to create or update TTL index on collection {collection} for field {field}",
                        _collection, ttlFieldName);
                }
            }
        }

        private bool InitializeCollection()
        {
            var isNewCollection = false;

            if (!_mongoClient.GetDatabase(_databaseName).ListCollectionNames().ToList().Contains(_collection))
            {
                isNewCollection = true;
                try
                {
                    _mongoClient.GetDatabase(_databaseName).CreateCollection(_collection);
                    Log.Logger.Information("Created new collection {collection} in database {database}", _collection, _databaseName);
                }
                catch (Exception e)
                {
                    Log.Logger.Error(e, "Fail to create collection {collection} in database {database}", _collection, _databaseName);
                }
            }

            return isNewCollection;
        }

        private void HandleTtlIndexCreation(string ttlFieldName, int ttlExpirationSeconds, bool forceTtlActivation, bool isNewCollection)
        {
            // Only proceed if it's a new collection OR if force activation is enabled
            if (!isNewCollection && !forceTtlActivation)
            {
                Log.Logger.Information("Collection {collection} already exists and ForceTtlIndexActivation is false. Skipping TTL index creation.", _collection);
                return;
            }

            var database = _mongoClient.GetDatabase(_databaseName);
            var collection = database.GetCollection<T>(_collection);

            // Check if TTL index already exists
            var existingTtlIndex = GetExistingTtlIndex(collection, ttlFieldName);

            if (existingTtlIndex != null)
            {
                Log.Logger.Information("TTL index already exists on field {field} in collection {collection}", ttlFieldName, _collection);

                // If force activation is enabled, update the existing index
                if (forceTtlActivation)
                {
                    UpdateTtlIndex(database, ttlFieldName, ttlExpirationSeconds);
                }
                return;
            }

            // Check if a regular index exists on the field
            var existingRegularIndex = GetExistingRegularIndex(collection, ttlFieldName);

            if (existingRegularIndex != null && (isNewCollection || forceTtlActivation))
            {
                // Convert existing regular index to TTL index using collMod
                ConvertToTtlIndex(database, ttlFieldName, ttlExpirationSeconds);
            }
            else if (existingRegularIndex == null)
            {
                // Create new TTL index
                CreateTtlIndex(collection, ttlFieldName, ttlExpirationSeconds);
            }
            else
            {
                Log.Logger.Warning("Regular index exists on field {field} in collection {collection}, but conditions not met for TTL conversion. TTL not applied.",
                    ttlFieldName, _collection);
            }
        }

        private BsonDocument GetExistingTtlIndex(IMongoCollection<T> collection, string fieldName)
        {
            try
            {
                var indexes = collection.Indexes.List().ToList();
                return indexes.FirstOrDefault(index =>
                    index.Contains("key") &&
                    index["key"].AsBsonDocument.Contains(fieldName) &&
                    index.Contains("expireAfterSeconds"));
            }
            catch (Exception e)
            {
                Log.Logger.Warning(e, "Failed to check for existing TTL index on field {field} in collection {collection}", fieldName, _collection);
                return null;
            }
        }

        private BsonDocument GetExistingRegularIndex(IMongoCollection<T> collection, string fieldName)
        {
            try
            {
                var indexes = collection.Indexes.List().ToList();
                return indexes.FirstOrDefault(index =>
                    index.Contains("key") &&
                    index["key"].AsBsonDocument.Contains(fieldName) &&
                    !index.Contains("expireAfterSeconds"));
            }
            catch (Exception e)
            {
                Log.Logger.Warning(e, "Failed to check for existing regular index on field {field} in collection {collection}", fieldName, _collection);
                return null;
            }
        }

        private void CreateTtlIndex(IMongoCollection<T> collection, string fieldName, int expirationSeconds)
        {
            try
            {
                var indexKeysDefinition = Builders<T>.IndexKeys.Ascending(fieldName);
                var indexOptions = new CreateIndexOptions
                {
                    ExpireAfter = TimeSpan.FromSeconds(expirationSeconds),
                    Name = $"{fieldName}_ttl"
                };

                collection.Indexes.CreateOne(new CreateIndexModel<T>(indexKeysDefinition, indexOptions));

                Log.Logger.Information("Created TTL index on field {field} in collection {collection} with expiration {seconds} seconds",
                    fieldName, _collection, expirationSeconds);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Failed to create TTL index on field {field} in collection {collection}", fieldName, _collection);
                throw;
            }
        }

        private void ConvertToTtlIndex(IMongoDatabase database, string fieldName, int expirationSeconds)
        {
            try
            {
                var command = new BsonDocument
                {
                    { "collMod", _collection },
                    { "index", new BsonDocument
                        {
                            { "keyPattern", new BsonDocument(fieldName, 1) },
                            { "expireAfterSeconds", expirationSeconds }
                        }
                    }
                };

                database.RunCommand<BsonDocument>(command);

                Log.Logger.Information("Converted existing index to TTL index on field {field} in collection {collection} with expiration {seconds} seconds",
                    fieldName, _collection, expirationSeconds);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Failed to convert existing index to TTL index on field {field} in collection {collection}", fieldName, _collection);
                throw;
            }
        }

        private void UpdateTtlIndex(IMongoDatabase database, string fieldName, int expirationSeconds)
        {
            try
            {
                var command = new BsonDocument
                {
                    { "collMod", _collection },
                    { "index", new BsonDocument
                        {
                            { "keyPattern", new BsonDocument(fieldName, 1) },
                            { "expireAfterSeconds", expirationSeconds }
                        }
                    }
                };

                database.RunCommand<BsonDocument>(command);

                Log.Logger.Information("Updated TTL index expiration on field {field} in collection {collection} to {seconds} seconds",
                    fieldName, _collection, expirationSeconds);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Failed to update TTL index on field {field} in collection {collection}", fieldName, _collection);
                throw;
            }
        }

        public void WarmUp()
        {
            var task = Collection.AsQueryable().ToListAsync();
            task.Wait();
        }

        public IQueryable<T> AsQueryable()
        {
            return Collection.AsQueryable();
        }

        public async Task<IEnumerable<T>> GetAll()
        {
            return await Collection.AsQueryable().ToListAsync();
        }

        public async Task<IEnumerable<T>> FilterByAsync(Expression<Func<T, bool>> filterExpression)
        {
            var res = await Collection.FindAsync(filterExpression);
             return res?.ToList();
        }

        public async Task<IEnumerable<T>> FilterByAsync(FilterDefinition<T> filterDefinition)
        {
            var res = await Collection.FindAsync(filterDefinition);
            return res?.ToList();
        }

        public async Task<T> FilterOneByAsync(Expression<Func<T, bool>> filterExpression)
        {
            var res = await Collection.FindAsync(filterExpression);
            return res?.FirstOrDefault();
        }

        public async Task<T> FilterOneByAsync(FilterDefinition<T> filterDefinition)
        {
            var res = await Collection.FindAsync(filterDefinition);
            return res?.FirstOrDefault();
        }

        public async Task<IEnumerable<TProjected>> FilterByWithProjection<TProjected>(
            Expression<Func<T, bool>> filterExpression,
            Expression<Func<T, TProjected>> projectionExpression)
        {
            return await Collection.Find(filterExpression).Project(projectionExpression)?.ToListAsync();
        }

        public async Task<IEnumerable<TProjected>> FilterByWithProjection<TProjected>(
            FilterDefinition<T> filterDefinition,
            Expression<Func<T, TProjected>> projectionExpression)
        {
            return await Collection.Find(filterDefinition).Project(projectionExpression)?.ToListAsync();
        }

        public async Task<T> FindByIdAsync(string id)
        {
            var filter = Builders<T>.Filter.Eq(s => s.Id, id);
            var res = await Collection.FindAsync(filter);
            return res?.SingleOrDefault();
        }

        public async Task InsertOneAsync(T document)
        {
            await Collection.InsertOneAsync(document);
        }

        public async Task InsertManyAsync(ICollection<T> documents)
        {
            await Collection.InsertManyAsync(documents);
        }

        public async Task ReplaceOneAsync(T document , bool insert = true)
        {
            var filter = Builders<T>.Filter.Eq(doc => doc.Id, document.Id);
            await Collection.FindOneAndReplaceAsync(filter, document,new FindOneAndReplaceOptions<T, T> {IsUpsert = insert });
        }

        public async Task DeleteOneAsync(Expression<Func<T, bool>> filterExpression)
        {
            await Collection.FindOneAndDeleteAsync(filterExpression);
        }

        public async Task DeleteOneAsync(FilterDefinition<T> filterDefinition)
        {
            await Collection.FindOneAndDeleteAsync(filterDefinition);
        }

        public async Task DeleteByIdAsync(string id)
        {
            var filter = Builders<T>.Filter.Eq(s => s.Id, id);
            await Collection.FindOneAndDeleteAsync(filter);
        }

        public async Task DeleteManyAsync(Expression<Func<T, bool>> filterExpression)
        {
            await Collection.DeleteManyAsync(filterExpression);
        }

        public async Task DeleteManyAsync(FilterDefinition<T> filterDefinition)
        {
            await Collection.DeleteManyAsync(filterDefinition);
        }

        public async Task<long> CountAsync(Expression<Func<T, bool>> filterExpression)
        {
            return await Collection.CountDocumentsAsync(filterExpression);
        }

        public async Task<long> CountAsync(FilterDefinition<T> filterDefinition)
        {
            return await Collection.CountDocumentsAsync(filterDefinition);
        }

        public async Task<bool> AnyAsync(Expression<Func<T, bool>> filterExpression)
        {
            return await Collection.Find(filterExpression).AnyAsync();
        }

        public async Task<bool> AnyAsync(FilterDefinition<T> filterDefinition)
        {
            return await Collection.Find(filterDefinition).AnyAsync();
        }

        public async Task<(int totalPages, IReadOnlyList<T> data)> AggregateByPage(FilterDefinition<T> filterDefinition,SortDefinition<T> sortDefinition,int page,int pageSize)
        {
            var countFacet = AggregateFacet.Create("count",
                PipelineDefinition<T, AggregateCountResult>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Count<T>()
                }));

            var dataFacet = AggregateFacet.Create("data",
                PipelineDefinition<T, T>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Sort(sortDefinition),
                PipelineStageDefinitionBuilder.Skip<T>((page - 1) * pageSize),
                PipelineStageDefinitionBuilder.Limit<T>(pageSize),
                }));


            var aggregation = await Collection.Aggregate()
                .Match(filterDefinition)
                .Facet(countFacet, dataFacet)
                .ToListAsync();

            var count = aggregation.First()
                .Facets.First(x => x.Name == "count")
                .Output<AggregateCountResult>()
                ?.FirstOrDefault()
                ?.Count;

            var totalPages = (int)Math.Ceiling((double)count / pageSize);

            var data = aggregation.First()
                .Facets.First(x => x.Name == "data")
                .Output<T>();

            return (totalPages, data);
        }

        public async Task<(int totalPages, IReadOnlyList<T> data)> AggregateByPage(Expression<Func<T, bool>> filterExpression, SortDefinition<T> sortDefinition, int page, int pageSize)
        {
            var countFacet = AggregateFacet.Create("count",
                PipelineDefinition<T, AggregateCountResult>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Count<T>()
                }));

            var dataFacet = AggregateFacet.Create("data",
                PipelineDefinition<T, T>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Sort(sortDefinition),
                PipelineStageDefinitionBuilder.Skip<T>((page - 1) * pageSize),
                PipelineStageDefinitionBuilder.Limit<T>(pageSize),
                }));


            var aggregation = await Collection.Aggregate()
                .Match(filterExpression)
                .Facet(countFacet, dataFacet)
                .ToListAsync();

            var count = aggregation.First()
                .Facets.First(x => x.Name == "count")
                .Output<AggregateCountResult>()
                ?.FirstOrDefault()
                ?.Count;

            var totalPages = (int)Math.Ceiling((double)count / pageSize);

            var data = aggregation.First()
                .Facets.First(x => x.Name == "data")
                .Output<T>();

            return (totalPages, data);
        }
    }
}