using ITF.Lib.Common.DomainDrivenDesign;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.MongoDB.Repository
{
    // https://alexalvess.medium.com/getting-started-with-net-core-api-mongodb-and-transactions-c7a021684d01
    public abstract class MongoRepository<T> : IMongoRepository<T> where T : BaseClass<string>
    {
        private readonly IMongoClient _mongoClient;
        private readonly string _collection;
        private readonly string _databaseName;
        public IMongoCollection<T> Collection { get; }
        public MongoRepository(IMongoClient mongoClient, string databaseName , string collection)
        {
            if (string.IsNullOrWhiteSpace(collection))
                throw new ArgumentNullException(nameof(collection));

            if (string.IsNullOrWhiteSpace(databaseName))
                throw new ArgumentNullException(nameof(databaseName));

            (_mongoClient, _databaseName, _collection) = (mongoClient, databaseName, collection);

            if (!_mongoClient.GetDatabase(_databaseName).ListCollectionNames().ToList().Contains(_collection))
                try
                {
                    _mongoClient.GetDatabase(_databaseName).CreateCollection(_collection);
                }
                catch (Exception e) 
                {
                    Log.Logger.Error(e, "Fail to create collection {collection} in database {database}", _collection, _databaseName);
                }

            Collection = _mongoClient.GetDatabase(_databaseName).GetCollection<T>(_collection);
        }

        public void WarmUp()
        {
            var task = Collection.AsQueryable().ToListAsync();
            task.Wait();
        }

        public IQueryable<T> AsQueryable()
        {
            return Collection.AsQueryable();
        }

        public async Task<IEnumerable<T>> GetAll()
        {
            return await Collection.AsQueryable().ToListAsync();
        }

        public async Task<IEnumerable<T>> FilterByAsync(Expression<Func<T, bool>> filterExpression)
        {
            var res = await Collection.FindAsync(filterExpression);
             return res?.ToList();
        }

        public async Task<IEnumerable<T>> FilterByAsync(FilterDefinition<T> filterDefinition)
        {
            var res = await Collection.FindAsync(filterDefinition);
            return res?.ToList();
        }

        public async Task<T> FilterOneByAsync(Expression<Func<T, bool>> filterExpression)
        {
            var res = await Collection.FindAsync(filterExpression);
            return res?.FirstOrDefault();
        }

        public async Task<T> FilterOneByAsync(FilterDefinition<T> filterDefinition)
        {
            var res = await Collection.FindAsync(filterDefinition);
            return res?.FirstOrDefault();
        }

        public async Task<IEnumerable<TProjected>> FilterByWithProjection<TProjected>(
            Expression<Func<T, bool>> filterExpression,
            Expression<Func<T, TProjected>> projectionExpression)
        {
            return await Collection.Find(filterExpression).Project(projectionExpression)?.ToListAsync();
        }

        public async Task<IEnumerable<TProjected>> FilterByWithProjection<TProjected>(
            FilterDefinition<T> filterDefinition,
            Expression<Func<T, TProjected>> projectionExpression)
        {
            return await Collection.Find(filterDefinition).Project(projectionExpression)?.ToListAsync();
        }

        public async Task<T> FindByIdAsync(string id)
        {
            var filter = Builders<T>.Filter.Eq(s => s.Id, id);
            var res = await Collection.FindAsync(filter);
            return res?.SingleOrDefault();
        }

        public async Task InsertOneAsync(T document)
        {
            await Collection.InsertOneAsync(document);
        }

        public async Task InsertManyAsync(ICollection<T> documents)
        {
            await Collection.InsertManyAsync(documents);
        }

        public async Task ReplaceOneAsync(T document , bool insert = true)
        {
            var filter = Builders<T>.Filter.Eq(doc => doc.Id, document.Id);
            await Collection.FindOneAndReplaceAsync(filter, document,new FindOneAndReplaceOptions<T, T> {IsUpsert = insert });
        }

        public async Task DeleteOneAsync(Expression<Func<T, bool>> filterExpression)
        {
            await Collection.FindOneAndDeleteAsync(filterExpression);
        }

        public async Task DeleteOneAsync(FilterDefinition<T> filterDefinition)
        {
            await Collection.FindOneAndDeleteAsync(filterDefinition);
        }

        public async Task DeleteByIdAsync(string id)
        {
            var filter = Builders<T>.Filter.Eq(s => s.Id, id);
            await Collection.FindOneAndDeleteAsync(filter);
        }

        public async Task DeleteManyAsync(Expression<Func<T, bool>> filterExpression)
        {
            await Collection.DeleteManyAsync(filterExpression);
        }

        public async Task DeleteManyAsync(FilterDefinition<T> filterDefinition)
        {
            await Collection.DeleteManyAsync(filterDefinition);
        }

        public async Task<long> CountAsync(Expression<Func<T, bool>> filterExpression)
        {
            return await Collection.CountDocumentsAsync(filterExpression);
        }

        public async Task<long> CountAsync(FilterDefinition<T> filterDefinition)
        {
            return await Collection.CountDocumentsAsync(filterDefinition);
        }

        public async Task<bool> AnyAsync(Expression<Func<T, bool>> filterExpression)
        {
            return await Collection.Find(filterExpression).AnyAsync();
        }

        public async Task<bool> AnyAsync(FilterDefinition<T> filterDefinition)
        {
            return await Collection.Find(filterDefinition).AnyAsync();
        }

        public async Task<(int totalPages, IReadOnlyList<T> data)> AggregateByPage(FilterDefinition<T> filterDefinition,SortDefinition<T> sortDefinition,int page,int pageSize)
        {
            var countFacet = AggregateFacet.Create("count",
                PipelineDefinition<T, AggregateCountResult>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Count<T>()
                }));

            var dataFacet = AggregateFacet.Create("data",
                PipelineDefinition<T, T>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Sort(sortDefinition),
                PipelineStageDefinitionBuilder.Skip<T>((page - 1) * pageSize),
                PipelineStageDefinitionBuilder.Limit<T>(pageSize),
                }));


            var aggregation = await Collection.Aggregate()
                .Match(filterDefinition)
                .Facet(countFacet, dataFacet)
                .ToListAsync();

            var count = aggregation.First()
                .Facets.First(x => x.Name == "count")
                .Output<AggregateCountResult>()
                ?.FirstOrDefault()
                ?.Count;

            var totalPages = (int)Math.Ceiling((double)count / pageSize);

            var data = aggregation.First()
                .Facets.First(x => x.Name == "data")
                .Output<T>();

            return (totalPages, data);
        }

        public async Task<(int totalPages, IReadOnlyList<T> data)> AggregateByPage(Expression<Func<T, bool>> filterExpression, SortDefinition<T> sortDefinition, int page, int pageSize)
        {
            var countFacet = AggregateFacet.Create("count",
                PipelineDefinition<T, AggregateCountResult>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Count<T>()
                }));

            var dataFacet = AggregateFacet.Create("data",
                PipelineDefinition<T, T>.Create(new[]
                {
                PipelineStageDefinitionBuilder.Sort(sortDefinition),
                PipelineStageDefinitionBuilder.Skip<T>((page - 1) * pageSize),
                PipelineStageDefinitionBuilder.Limit<T>(pageSize),
                }));


            var aggregation = await Collection.Aggregate()
                .Match(filterExpression)
                .Facet(countFacet, dataFacet)
                .ToListAsync();

            var count = aggregation.First()
                .Facets.First(x => x.Name == "count")
                .Output<AggregateCountResult>()
                ?.FirstOrDefault()
                ?.Count;

            var totalPages = (int)Math.Ceiling((double)count / pageSize);

            var data = aggregation.First()
                .Facets.First(x => x.Name == "data")
                .Output<T>();

            return (totalPages, data);
        }
    }
}