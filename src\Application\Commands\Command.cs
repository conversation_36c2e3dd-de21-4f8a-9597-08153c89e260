﻿using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
namespace IT.Microservices.CT.Order.Wrapper.Application.Commands;

public static class Command
{
    public static class V1
    {
        public record UpdateInProgress(string ExecutingFloristId, string OrderNumber, string OrderId, long OrderVersion, bool Value);
        public record UpdateStatus(string ExecutingFloristId, string Action, string OrderNumber, string CommerceToolsOrderId, long CommerceToolsOrderVersion, string ModifyBy, string Reason, string ReasonCode);
        public record UpdateNote(OrderNoteDto OrderNoteDto);
        public record InsertNote(OrderNoteDto OrderNoteDto);
    }
}
