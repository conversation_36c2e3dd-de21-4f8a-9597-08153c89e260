﻿using commercetools.Base.Client.Error;
using Elastic.Apm;
using FluentValidation.Results;
using IT.Microservices.CT.Order.Wrapper.Application;
using IT.Microservices.CT.Order.Wrapper.Application.Commands;
using IT.Microservices.CT.Order.Wrapper.Domain.Exceptions;
using IT.Microservices.CT.Order.Wrapper.Domain.Validator;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.Orders;
using ITF.SharedModels.DataModels.Globals;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using static IT.Microservices.CT.Order.Wrapper.Domain.Exceptions.Exceptions;
using static ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;

namespace IT.Microservices.CT.Order.Wrapper.Presentation;

public class UpdateOrderController(IOrderUpdateService orderUpdateService, ILogger<UpdateOrderController> logger, IOrderActionsFacade orderActionsFacade, IOrderQueryService orderQueryService) : BaseController
{
    [HttpPost("UpdateInProgress")]
    public async Task<IActionResult> UpdateInProgress(Command.V1.UpdateInProgress cmd)
    {

        TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", cmd.OrderNumber);
        TagTransaction(Agent.Tracer.CurrentTransaction, "ExecutingFloristId", cmd.ExecutingFloristId);
        try
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(UpdateInProgress), Newtonsoft.Json.JsonConvert.SerializeObject(cmd));
        }
        catch { }
        var order = await orderUpdateService.UpdateCustomField(cmd.OrderId, cmd.OrderVersion, cmd.OrderNumber, CtOrderCustomAttributesNames.Order.DELIVERY_IN_PROGRESS, cmd.Value, true);
        if (order == null)
        {
            return NotFound();
        }

        logger.LogInformation("Result {method}: {result}", nameof(UpdateInProgress), order.OrderNumber);
        return new OkObjectResult(order.OrderNumber);
    }

    [HttpPost("UpdateReadByExecutingFlorist")]
    public async Task<IActionResult> UpdateReadByExecutingFlorist(Command.V1.UpdateInProgress cmd)
    {
        IActionResult result = null;

        TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", cmd.OrderNumber);
        TagTransaction(Agent.Tracer.CurrentTransaction, "ExecutingFloristId", cmd.ExecutingFloristId);

        try
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(UpdateReadByExecutingFlorist), Newtonsoft.Json.JsonConvert.SerializeObject(cmd));
        }
        catch { }

        result = await ProcessUpdateReadByExecutingFlorist(cmd);
        return result;
    }

    private async Task<IActionResult> ProcessUpdateReadByExecutingFlorist(Command.V1.UpdateInProgress cmd)
    {
        commercetools.Sdk.Api.Models.Orders.Order order;
        try
        {
            order = await orderQueryService.GetOrderById(cmd.OrderId);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.Message);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }

        if (order == null)
        {
            return NotFound();
        }

        TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", cmd.OrderNumber);
        TagTransaction(Agent.Tracer.CurrentTransaction, "ExecutingFloristId", cmd.ExecutingFloristId);
        TagTransaction(Agent.Tracer.CurrentTransaction, "OrderReference", order.Id);

        await orderUpdateService.UpdateReadByExecutingFloristCustomFieldForced(cmd.OrderId, order.Version, order.OrderNumber, cmd.Value);

        logger.LogInformation("Result {method}: {result}", nameof(UpdateReadByExecutingFlorist), order.OrderNumber);
        return new OkObjectResult(order.OrderNumber);
    }

    [SwaggerOperation(
    Summary = "Update the status of an order",
    Description = "The CommerceTools custom field \"floristOrderStatus\" of the order. Possible values for the \"action\" parameter are : \"accept, refuse, reassign, deliver, is_absent and cancel\" in lower case. ",
    OperationId = "UpdateOrderStatus")]
    [SwaggerResponse(200, "The order number")]
    [SwaggerResponse(400, "A parameter is invalid")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("UpdateOrderStatus")]
    public async Task<IActionResult> UpdateOrderStatusAsync(Command.V1.UpdateStatus cmd)
    {
        IActionResult result = null;

        TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", cmd?.OrderNumber != null ? cmd.OrderNumber.ToString() : null);
        TagTransaction(Agent.Tracer.CurrentTransaction, "ExecutingFloristId", cmd.ExecutingFloristId);
        TagTransaction(Agent.Tracer.CurrentTransaction, "Action", cmd.Action);

        try
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(UpdateOrderStatusAsync), Newtonsoft.Json.JsonConvert.SerializeObject(cmd));
        }
        catch (Exception) { }

        result = await ProcessUpdateOrderStatuAsync(cmd);

        return result;

    }


    [SwaggerOperation(
    Summary = "Update the status of an order to DELIVERED",
    Description = " Used to change the status of the order to DELIVERED when reading the QR code from an order form. The CommerceTools custom field \"floristOrderStatus\" of the order. Possible values for the \"action\" parameter are : \"accept, refuse, reassign, deliver, is_absent and cancel\" in lower case. ",
    OperationId = "UpdateOrderToDelivered")]
    [SwaggerResponse(200, "The order number")]
    [SwaggerResponse(400, "A parameter is invalid")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("UpdateOrderToDelivered")]
    public async Task<IActionResult> UpdateOrderToDelivered(string p)
    {

        try
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(UpdateOrderToDelivered), p);
        }
        catch { }

        UpdateStatusToDeliveredResult result = null;
        try
        {

            result = await orderActionsFacade.UpdateStatusToDeliveredAsync(p);

            TagTransaction(Agent.Tracer.CurrentTransaction, "OrderReference", result.OrderId);
            TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", result.OrderNumber);
        }
        catch (OrderNotFoundException e)
        {
            logger.LogError($"Error OrderNotFoundException on UpdateOrderStatusAsync({p}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new NotFoundObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (OrderStatusNotConsistentException e)
        {
            logger.LogError($"Error OrderStatusNotConsistentException on UpdateOrderStatusAsync({p}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (LegacyBackendExceptions.InvariantException e)
        {
            logger.LogError($"Error InvariantException on UpdateOrderStatusAsync({p}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(e.Message);
        }
        catch (LegacyBackendExceptions.LegacyBackendInfrastructureException e)
        {
            logger.LogError($"Error LegacyBackendInfrastructureException on UpdateOrderStatusAsync({p}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new UnauthorizedObjectResult(new ErrorObjectReponse(e.Message, e.Message, logger));
        }
        catch (IncorrectOrderActionException e)
        {
            logger.LogError($"Error IncorrectOrderActionException on UpdateOrderStatusAsync({p}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (Exception e)
        {
            logger.LogError($"Error Exception on UpdateOrderStatusAsync({p}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(e.Message);
        }

        logger.LogInformation("Result {method}: {result}", nameof(UpdateOrderToDelivered), result.OrderNumber);
        return new OkObjectResult(result.OrderNumber);
    }

    [SwaggerOperation(
    Summary = "Update a of an order",
    Description = "The Mongo order note object linked to the order. String with 255 caracters limit for the content",
    OperationId = "UpdateOrderNote")]
    [SwaggerResponse(200, "The order number")]
    [SwaggerResponse(400, "A parameter is invalid")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("UpdateOrderNote")]
    public async Task<IActionResult> UpdateOrderNoteAsync(Command.V1.UpdateNote cmd)
    {
        try
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(UpdateOrderNoteAsync), Newtonsoft.Json.JsonConvert.SerializeObject(cmd));
        }
        catch { }
        string orderNumber;

        var validation = ValidateNote(cmd.OrderNoteDto);
        if (!validation.IsValid)
        {
            return GetResultFromValidationErrors(validation.Errors);
        }

        try
        {
            orderNumber = await orderActionsFacade.UpdateNoteAsync(cmd);
            TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", orderNumber);
        }
        catch (OrderNotFoundException e)
        {
            return new NotFoundObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (InvalidNoteContentLengthException e)
        {
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (NoteNotFoundException e)
        {
            return new NotFoundObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }

        logger.LogInformation("Result {method}: {result}", nameof(UpdateOrderNoteAsync), orderNumber);
        return new OkObjectResult(orderNumber);
    }

    [SwaggerOperation(
   Summary = "Insert a note of an order",
    Description = "The Mongo order note object linked to the order. String with 255 caracters limit for the content",
    OperationId = "InsertOrderNote")]
    [SwaggerResponse(200, "The order number")]
    [SwaggerResponse(400, "A parameter is invalid")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("PostOrderNote")]
    public async Task<IActionResult> InsertOrderNoteAsync(Command.V1.InsertNote cmd)
    {
        try
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(InsertOrderNoteAsync), Newtonsoft.Json.JsonConvert.SerializeObject(cmd));
        }
        catch { }
        string orderNumber;

        var validation = ValidateNote(cmd.OrderNoteDto);
        if (!validation.IsValid)
        {
            return GetResultFromValidationErrors(validation.Errors);
        }

        try
        {
            orderNumber = await orderActionsFacade.InsertNoteAsync(cmd);
            TagTransaction(Agent.Tracer.CurrentTransaction, "OrderNumber", orderNumber);
        }
        catch (OrderNotFoundException e)
        {
            return new NotFoundObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (InvalidNoteContentLengthException e)
        {
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }

        logger.LogInformation("Result {method}: {result}", nameof(InsertOrderNoteAsync), orderNumber);
        return new OkObjectResult(orderNumber);
    }

    private static ValidationResult ValidateNote(OrderNoteDto dto)
    {
        var validator = new OrderNoteValidator();
        return validator.Validate(dto);

    }

    private IActionResult GetResultFromValidationErrors(List<ValidationFailure> errors)
    {
        string errorMessage = string.Empty;
        errors.ForEach(error => errorMessage += String.Join(", ", error.ErrorMessage));
        return new BadRequestObjectResult(new ErrorObjectReponse("IncorrectOrderNoteException", errorMessage, logger));
    }

    private async Task<IActionResult> ProcessUpdateOrderStatuAsync(Command.V1.UpdateStatus cmd)
    {
        string orderNumber;
        string parameters = string.Empty;
        try
        {
            orderNumber = await orderActionsFacade.UpdateStatusAsync(cmd);

            parameters = (string.IsNullOrWhiteSpace(cmd.OrderNumber) ? "null" : cmd.OrderNumber) +
                "," + (string.IsNullOrWhiteSpace(cmd.CommerceToolsOrderId) ? "null" : cmd.CommerceToolsOrderId) +
                "," + (string.IsNullOrWhiteSpace(cmd.ExecutingFloristId) ? "null" : cmd.ExecutingFloristId) +
                "," + (string.IsNullOrWhiteSpace(cmd.Reason) ? "null" : cmd.Reason) +
                "," + (string.IsNullOrWhiteSpace(cmd.ReasonCode) ? "null" : cmd.ReasonCode) +
                "," + (string.IsNullOrWhiteSpace(cmd.Action) ? "null" : cmd.Action);
        }
        catch (OrderNotFoundException e)
        {
            logger.LogError($"Error OrderNotFoundException on UpdateOrderStatusAsync({parameters}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new NotFoundObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (OrderStatusNotConsistentException e)
        {
            logger.LogError($"Error OrderStatusNotConsistentException on UpdateOrderStatusAsync({parameters}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (LegacyBackendExceptions.InvariantException e)
        {
            logger.LogError($"Error InvariantException on UpdateOrderStatusAsync({parameters}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(e.Message);
        }
        catch (LegacyBackendExceptions.LegacyBackendInfrastructureException e)
        {
            logger.LogError($"Error LegacyBackendInfrastructureException on UpdateOrderStatusAsync({parameters}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new UnauthorizedObjectResult(new ErrorObjectReponse(e.Message, e.Message, logger));
        }
        catch (IncorrectOrderActionException e)
        {
            logger.LogError($"Error IncorrectOrderActionException on UpdateOrderStatusAsync({parameters}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message, logger));
        }
        catch (Exception e)
        {
            logger.LogError($"Error Exception on UpdateOrderStatusAsync({parameters}) - Message: {e.Message} ::: StackTrace: {e.StackTrace}");
            return new BadRequestObjectResult(e.Message);
        }

        logger.LogInformation("Result {method}: {result}", nameof(UpdateOrderStatusAsync), orderNumber);
        return new OkObjectResult(orderNumber);
    }
}
