using commercetools.Sdk.Api;
using IT.Microservices.CT.Order.Wrapper.Application;
using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using IT.SharedLibraries.CT.Carts;
using IT.SharedLibraries.CT.Channels;
using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.Products;
using IT.SharedLibraries.CT.ProductTypes;
using IT.SharedLibraries.CT.Settings;
using IT.SharedLibraries.CT.ShippingMethods;
using IT.SharedLibraries.CT.Stores;
using ITE.Order.Library.Infrastructure;
using ITF.Order.Library.Infrastructure;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ApplicationMetrics.Extensions;
using ITF.SharedLibraries.Authentication.Keycloack;
using ITF.SharedLibraries.Availability;
using ITF.SharedLibraries.ElasticSearch.Extensions;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.FeatureFlags;
using ITF.SharedLibraries.Framework;
using ITF.SharedLibraries.HealthCheck.Extensions;
using ITF.SharedLibraries.Kafka.Extensions;
using ITF.SharedLibraries.Kafka.Publisher;
using ITF.SharedLibraries.MongoDB.Extensions;
using ITF.SharedLibraries.RAO;
using ITF.SharedLibraries.Readyness.Extensions;
using ITF.SharedLibraries.Swagger;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Prometheus;
using System.Reflection;
using static ITF.SharedLibraries.HttpClient.Polly.Extensions;

namespace IT.Microservices.CT.Order.Wrapper;

public class Startup(IConfiguration configuration)
{
    public IConfiguration Configuration { get; } = configuration;

    private const string KEYCLOAK_CONFIG_NAME = "KeycloakBasic";

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        services.Configure<KafkaTopicsSettings>(Configuration.GetSection("KafkaTopicsSettings"));
        services.Configure<CommerceToolCustomSettings>(Configuration.GetSection("CommerceToolCustomSettings"));
        services.Configure<LegacyBackendEndpointList>(Configuration.GetSection("LegacyBackendEndpointList"));
        services.Configure<OrderConfirmationSettings>(Configuration.GetSection("OrderConfirmationSettings"));

        services.Configure<CTOrderWrapperSettings>(Configuration.GetSection("CTOrderWrapperSettings"));

        var legacyBackendSlaveEndpointList = Configuration.GetSection("LegacyBackendSlaveEndpointList");
        if (legacyBackendSlaveEndpointList != null)
        {
            services.Configure<LegacyBackendSlaveEndpointList>(legacyBackendSlaveEndpointList);
        }

        try
        {
            var configuration = Configuration.Get<BasicConfiguration>(KEYCLOAK_CONFIG_NAME);
            services.UseKeycloakBasicAuthentication(Configuration, KEYCLOAK_CONFIG_NAME);
        }
        catch { }

        // MongoDB
        services.UseMongoDb(Configuration);

        Setup(services);

        // Slack Alert Service
        services.AddSlackAlertService(Configuration);

        // Commercetools API
        var registry = services.AddPolicyRegistry();
        AddRetryPolicy(registry,
            backoff: new TimeSpan[]
            {
                TimeSpan.FromMilliseconds(10),
                TimeSpan.FromMilliseconds(20),
                TimeSpan.FromMilliseconds(50),
                TimeSpan.FromMilliseconds(100)
            },
            policyName: "CTRetryPolicy");
        AddTimeOutPolicy(registry,
            timeOut: TimeSpan.FromSeconds(120),
            policyName: "CTTimeOut");

        services.UseCommercetoolsApiSerialization();
        services.UseCommercetoolsApi(Configuration, "Client")
            .AddPolicyHandlerFromRegistry("CTRetryPolicy")
            .AddPolicyHandlerFromRegistry("CTTimeOut");

        // Slack Alert Service
        services.AddSlackAlertService(Configuration);
        // Buisiness
        services.AddSingleton<ISequenceGeneratorService, SequenceGeneratorService>();
        services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IOrderQueryService, OrderQueryService>();
        services.AddScoped<IOrderUpdateService, OrderUpdateService>();
        services.AddScoped<IOrderValidationFacade, OrderValidationFacade>();
        services.AddScoped<IOrderActionsFacade, OrderActionsFacade>();
        services.AddScoped<IOrderQueryFacade, OrderQueryFacade>();
        services.AddScoped<IShippingUseCase, ShippingUseCase>();
        services.AddSingleton<IOrderActionLogRepository, OrderActionLogRepository>();
        services.AddSingleton<IOrderNoteRepository, OrderNoteRepository>();
        services.AddSingleton<IFloristRepository, FloristRepository>();
        services.AddSingleton<IOrderNotificationRepository, OrderNotificationRepository>();
        services.AddScoped<ICTProductSearchService, CTProductSearchService>();
        services.AddSingleton<ILegacyBackendService, LegacyBackendService>();
        services.AddSingleton<ILegacyBackendSlaveService, LegacyBackendSlaveService>();
        services.AddSingleton<IShippingMethodService, ShippingMethodService>();
        services.AddSingleton<ICartService, CartService>();
        services.AddSingleton<ICategoryService, CategoryService>();
        services.AddSingleton<IStoreService, StoreService>();
        services.AddSingleton<IChannelService, ChannelService>();
        services.AddSingleton<IProductTypeService, ProductTypeService>();
        services.AddSingleton<IOrderService, OrderService>();
        services.AddSingleton<ICategoryService, CategoryService>();
        services.AddSingleton<IProductService, ProductService>();
        services.AddScoped<IKafkaPublisher, KafkaPublisher>();
        services.AddScoped<IOrderLogHistoryKafkaPublisherHelper, OrderLogHistoryKafkaPublisherHelper>();
        // Kafka
        services.UseKafkaPublisher(Configuration);
        services.UseKafkaHealthChecker(Configuration);

        // Kafka
        //services.UseKafkaSubscribers<string, string>(Configuration,
        //    kafkaActionHandlers: new KafkaDelegateHandler[] {
        //            KafkaHandlerSupplier<IMessageHandler, OrderHandler>
        //    });

        // HealthChecks
        services.AddHealthChecksMiddleware();

        // Metrics
        services.AddAllMetrics();
			
        // Feature Flags
        services.UseFeatureFlags(Configuration);

        services.AddCors(options =>
        {
            options.AddPolicy("DevPolicy",
                policy =>
                {
                    policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
                });
        });

        services
            .AddControllers()
            .SuppressAutoACR();

        // Swagger
        services.AddSwagger(Assembly.GetExecutingAssembly().GetName().Name);

        services.AddHttpClientWithPolicy<IRAOSupplierHttpService, RAOSupplierHttpService>(Configuration, "RAOEndpoint");
        services.AddHttpClientWithPolicy<IAvailabilityHttpService, AvailabilityHttpService>(Configuration, "AvailabilityEndpoint");
    }

    private void Setup(IServiceCollection services)
    {
        var config = Configuration.Get<CTOrderWrapperSettings>("CTOrderWrapperSettings");

        if (config.CountryCode == "ES" || config.CountryCode == "PT" || config.CountryCode == "IT2")
        {
            services.UseSpanishOrderRules(Configuration);
            
        }
        else if (config.CountryCode == "FR")
        {
            services.UseFrenchOrderRules(Configuration);
        }
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IApiVersionDescriptionProvider provider)
    {
        // Distributed logs
        app.UseElasticSearchAPM(Configuration);

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseSwaggerEndpoint(provider, "itctorderwrapper");

        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization();

        // must be placed after UseRouting and before Authorization
        app.UseCors("DevPolicy");

        // Metrics middleware
        app.UseAllMetricsMiddleware()
            .UseMiddleware<RequestMiddleware>();

        app.UseEndpoints(endpoints =>
        {
				// HealthChecks
            endpoints.UseHealthChecks();

				// Readyness
            endpoints.UseReadynessRoute();
				
				// Metrics
            endpoints.MapMetrics();

            // if we have a configuration for Keycloak, we add authorization
            // by doing this, we can add authorization only if the configuration is present, instead of adding it in any cases with the [Authorize] annotation
            
            try { 
                var keycloakConfigSection = Configuration.GetRequiredSection(KEYCLOAK_CONFIG_NAME);
                endpoints.MapControllers().RequireAuthorization(new AuthorizeAttribute());
            }
            catch
            {
                endpoints.MapControllers();
            }
        });
    }
}
