﻿using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using Microsoft.AspNetCore.Mvc;

namespace IT.Microservices.CT.Order.Wrapper.Presentation;

public class QueryOrderActionLogController(IOrderActionLogRepository orderActionLogRepository) : BaseController
{
    [HttpGet("GetByOrderNumber")]
    public async Task<IActionResult> GetByOrderNumber(string orderNumber)
    {
        var logs = await orderActionLogRepository.FilterByAsync(l => l.OrderNumber.Equals(orderNumber));
        return Ok(logs);
    }
}
