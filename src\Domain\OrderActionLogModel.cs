﻿using ITF.Lib.Common.DomainDrivenDesign;

namespace IT.Microservices.CT.Order.Wrapper.Domain;

public class OrderActionLogModel : BaseClass<string>
{
    public override void SetId() => Id = Guid.NewGuid().ToString();

    public string ExecutingFloristId { get; set; }
    public string OrderNumber { get; set; }
    public string CommerceToolsID { get; set; }
    public string OrderAction { get; set; }
    public DateTime LogDate { get; set; }
    public string InitialOrderStatus { get; set; }
    public string Log { get; set; }
    public decimal OrderAmount { get; set; }
    public bool IsOnTime { get; set; }
}
