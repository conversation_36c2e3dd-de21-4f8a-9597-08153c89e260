﻿using IT.SharedLibraries.CT.Orders.DTO;

namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class OrdersStatusListsDTO
{
    public List<OrderSummaryDTO> ToAccept { get; set; } = new();
    public List<OrderSummaryDTO> ToPrepare { get; set; } = new();
    public List<OrderSummaryDTO> InDelivery { get; set; } = new();
    public List<OrderSummaryDTO> InProgress { get; set; } = new();
    //public List<OrderSummaryDTO> DeliveryCourierRequested { get; set; } = new();
    public List<OrderSummaryDTO> Absent { get; set; } = new();
    public List<OrderSummaryDTO> Late { get; set; } = new();
    public List<OrderSummaryDTO> Other { get; set; } = new();
}


