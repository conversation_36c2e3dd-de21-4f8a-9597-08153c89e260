﻿using commercetools.Base.Client.Error;
using IT.Microservices.CT.Order.Wrapper.Application;
using IT.Microservices.CT.Order.Wrapper.Domain.Queries;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace IT.Microservices.CT.Order.Wrapper.Presentation;

public class QueryProductsController(ICTProductSearchService productSearchService) : BaseController
{
    [SwaggerOperation(
    Summary = "Search products by id or name",
    Description = "Search products by key or name.",
    OperationId = "Search")]
    [SwaggerResponse(200, "A list of products DTO", typeof(ProductSearchDTO))]
    [SwaggerResponse(404, "The product info can't be found")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("Search")]
    public async Task<IActionResult> Search([FromBody] Query.V1.GetByKeyOrName query)
    {
        try { 
            await productSearchService.Init();
            var productSearchList = await productSearchService.FindProductByKeyOrName(query.Search);
            var productSearchDTOList = productSearchService.ToProductSearchDTOList(productSearchList);
            return new OkObjectResult(productSearchDTOList);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.ToString());
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult(e.ToString());
        }
    }

    [SwaggerOperation(
    Summary = "Get all products for the local country category",
    Description = "Get all products for the local country category. The local country category is set with CommerceToolCustomSettings.LocalCountryProductsCategoryKey in settings.",
    OperationId = "AllProducts")]
    [SwaggerResponse(200, "A list of products DTO", typeof(ProductSearchDTO))]
    [SwaggerResponse(404, "The product info can't be found")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpGet("AllProducts")]
    public async Task<IActionResult> AllProducts()
    {
        try
        {
            await productSearchService.Init();
            var productSearchList = await productSearchService.GetAllProducts();
            var productSearchListSearchDTOList = productSearchService.ToProductSearchDTOList(productSearchList);
            return new OkObjectResult(productSearchListSearchDTOList.OrderBy(x => x.Name));
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.ToString());
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult(e.ToString());
        }
    }

    [SwaggerOperation(
    Summary = "Get accessories for the parametrized country",
    Description = "",
    OperationId = "GetAccessories")]
    [SwaggerResponse(200, "A list of products DTO", typeof(ProductSearchDTO))]
    [SwaggerResponse(404, "The product info can't be found")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpGet("GetAccessories")]
    public async Task<IActionResult> GetAccessories()
    {
        try
        {
            await productSearchService.Init();
            var accSearchList = await productSearchService.GetAccessories();
            var accSearchDTOList = productSearchService.ToProductSearchDTOList(accSearchList);
            return new OkObjectResult(accSearchDTOList);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.ToString());
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult(e.ToString());
        }
    }

    [SwaggerOperation(
    Summary = "Get ONE product by it's key",
    Description = "",
    OperationId = "GetProductByKey")]
    [SwaggerResponse(200, "A product DTO", typeof(ProductSearchDTO))]
    [SwaggerResponse(404, "The product info can't be found")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("GetProductByKey")]
    public async Task<IActionResult> GetProductByKeyPost([FromBody] Query.V1.GetByKey query)
    {
        try
        {
            await productSearchService.Init();
            var productSearch = await productSearchService.GetProductByKey(query.Key);
            var productSearchDTO = productSearchService.ToProductSearchDTO(productSearch);
            if (productSearchDTO == null)
                return NotFound();

            return new OkObjectResult(productSearchDTO);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.ToString());
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult(e.ToString());
        }
    }

    [SwaggerOperation(
    Summary = "Get ONE product by it's key. GET version",
    Description = "",
    OperationId = "GetProduct")]
    [SwaggerResponse(200, "A product DTO", typeof(ProductSearchDTO))]
    [SwaggerResponse(404, "The product info can't be found")]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpGet("GetProduct/{productKey}")]
    public async Task<IActionResult> GetProduct(string productKey)
    {
        try
        {
            await productSearchService.Init();
            var productSearch = await productSearchService.GetProductByKey(productKey);
            var productSearchDTO = productSearchService.ToProductSearchDTO(productSearch);
            if (productSearchDTO == null)
                return NotFound();

            return new OkObjectResult(productSearchDTO);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.ToString());
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult(e.ToString());
        }
    }
}
