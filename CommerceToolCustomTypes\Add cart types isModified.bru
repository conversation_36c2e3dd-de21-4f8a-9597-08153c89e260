meta {
  name: Add cart types isModified
  type: http
  seq: 4
}

post {
  url: https://api.europe-west1.gcp.commercetools.com/{{CTP_PROJECT_KEY}}/types/key=cart-custom
  body: json
  auth: bearer
}

auth:bearer {
  token: {{TOKEN}}
}

body:json {
  {
    "actions": [
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {
          "name": "deliveryServiceRequested",
        "label": {
          "en": "Delivery Service requested"
        },
        "required": false,
        "type": {
          "name": "Boolean"
        },
        "inputHint": "SingleLine"
        }
      },
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {
          "name": "deliveryService",
        "label": {
          "en": "Delivery Service"
        },
        "required": false,
        "type": {
          "name": "String"
        },
        "inputHint": "SingleLine"
        }
      },
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {
         "name": "deliveryScheduledDate",
        "label": {
          "en": "Delivery Scheduled Date"
        },
        "required": false,
        "type": {
          "name": "DateTime"
        },
        "inputHint": "SingleLine"
        }
      },
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {
         "name": "deliveryStatus",
        "label": {
          "en": "Delivery Status"
        },
        "required": false,
        "type": {
          "name": "String"
        },
        "inputHint": "SingleLine"
        }
      },
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {
           "name": "deliveryCost",
        "label": {
          "en": "Delivery Cost"
        },
        "required": false,
        "type": {
          "name": "Money"
        },
        "inputHint": "SingleLine"
        }
      }
    ],
    "version": {{CART_CUSTOM_VERSION}}
  }
}
