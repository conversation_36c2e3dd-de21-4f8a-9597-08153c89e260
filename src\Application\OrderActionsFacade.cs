﻿using commercetools.Base.Client.Error;
using Elastic.Apm;
using IT.Microservices.CT.Order.Wrapper.Application.Commands;
using IT.Microservices.CT.Order.Wrapper.Domain;
using IT.Microservices.CT.Order.Wrapper.Domain.Exceptions;
using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using ITF.SharedLibraries.Cryptography;
using ITF.SharedLibraries.RAO;
using ITF.SharedLibraries.RAO.DTO;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Group.Order;
using Microsoft.Extensions.Options;
using System.Web;
using static IT.Microservices.CT.Order.Wrapper.Domain.Exceptions.Exceptions;
using static ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;

namespace IT.Microservices.CT.Order.Wrapper.Application;

public class OrderAction
{
    public string Action { get; set; }
    public string NewStatus { get; set; }
    public List<string> PreviousStatusNeeded { get; set; }
    public bool ResetExecutingState { get; set; }
}

public interface IOrderActionsFacade
{
    Task<string> UpdateStatusAsync(Command.V1.UpdateStatus cmd);
    Task<UpdateStatusToDeliveredResult> UpdateStatusToDeliveredAsync(string p);
    Task<string> InsertNoteAsync(Command.V1.InsertNote cmd);
    Task<string> UpdateNoteAsync(Command.V1.UpdateNote cmd);
}

public class UpdateStatusToDeliveredResult
{
    public string OrderId { get; internal set; }
    public string FloristId { get; internal set; }
    public string OrderNumber { get; internal set; }
}

public class OrderActionsFacade: IOrderActionsFacade
{
    private readonly IConfiguration _configuration;
    private readonly IOrderService _orderService;
    private readonly IOrderQueryService _orderQueryService;
    private readonly IOrderUpdateService _orderUpdateService;
    private readonly IOrderActionLogRepository _orderActionLogRepository;
    private readonly IOrderNoteRepository _orderNoteRepository;
    private readonly ILegacyBackendService _backendService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IRAOSupplierHttpService _RAOSupplierHttpService;
    private readonly ILogger<OrderActionsFacade> _logger;
    private readonly IOrderLogHistoryKafkaPublisherHelper _orderLogHistoryKafkaPublisherHelper;
    private readonly IOptionsMonitor<KafkaTopicsSettings> _kafkaTopicsSettings;
    private readonly IOptionsMonitor<OrderConfirmationSettings> _orderConfirmationSettings;
    private readonly IOptionsMonitor<CTOrderWrapperSettings> _ctOrderWrapperSettings;

    public OrderActionsFacade(IConfiguration configuration,
        IOrderService orderService,
        IOrderQueryService orderQueryService,
        IOrderUpdateService orderUpdateService,
        IOrderActionLogRepository orderActionLogRepository,
        IOrderNoteRepository orderNoteRepository,
        ILegacyBackendService backendService,
        IServiceProvider serviceProvider,
        IRAOSupplierHttpService RAOSupplierHttpService,
        ILogger<OrderActionsFacade> logger,
        IOrderLogHistoryKafkaPublisherHelper orderLogHistoryKafkaPublisherHelper,
        IOptionsMonitor<KafkaTopicsSettings> kafkatopicSettings,
        IOptionsMonitor<OrderConfirmationSettings> orderConfirmationSettings,
        IOptionsMonitor<CTOrderWrapperSettings> ctOrderWrapperSettings)
    {
        _configuration = configuration;
        _orderService = orderService;
        _orderQueryService = orderQueryService;
        _orderUpdateService = orderUpdateService;
        _orderActionLogRepository = orderActionLogRepository;
        _orderNoteRepository = orderNoteRepository;
        _backendService = backendService;
        _serviceProvider = serviceProvider;
        _RAOSupplierHttpService = RAOSupplierHttpService;
        _logger = logger;
        _orderLogHistoryKafkaPublisherHelper = orderLogHistoryKafkaPublisherHelper;
        _kafkaTopicsSettings = kafkatopicSettings;
        _orderConfirmationSettings = orderConfirmationSettings;
        _ctOrderWrapperSettings = ctOrderWrapperSettings;
    }

    public async Task<UpdateStatusToDeliveredResult> UpdateStatusToDeliveredAsync(string p)
    {
        if(_orderConfirmationSettings?.CurrentValue?.Base64Key_256bits == null)
        {
            throw new Exception("Base64Key_256bits missed");
        }
        if (_orderConfirmationSettings?.CurrentValue?.Base64Iv_128bits == null)
        {
            throw new Exception("Base64Iv_128bits missed");
        }

        UpdateStatusToDeliveredResult result = new UpdateStatusToDeliveredResult();
        byte[] key = new byte[32]; // 256-bit key
        byte[] iv = new byte[16]; // 128-bit IV
        key = Convert.FromBase64String(_orderConfirmationSettings?.CurrentValue?.Base64Key_256bits);
        iv = Convert.FromBase64String(_orderConfirmationSettings?.CurrentValue?.Base64Iv_128bits);

        var urlParamDecoded = HttpUtility.UrlDecode(p);
        var urlParamDecodedFromBAse64 = Convert.FromBase64String(urlParamDecoded);
        var urlParamDechipered = AesEncryption.Decrypt(urlParamDecodedFromBAse64, key, iv);
        var urlParamDechiperedParts = urlParamDechipered.Split(new char[1] { '#' });
        result.OrderId = urlParamDechiperedParts[0];
        result.FloristId = urlParamDechiperedParts[1];

        Command.V1.UpdateStatus cmd = new Command.V1.UpdateStatus(result.FloristId, StatusEnum.DELIVERED.ToString(), result.OrderId, null, 0, result.FloristId, null, null);

        result.OrderNumber = await UpdateStatusAsync(cmd);
        return result;
    }

    public async Task<string> UpdateStatusAsync(Command.V1.UpdateStatus cmd)
    {
        OrderAction orderAction = getOrderAction(cmd.Action);
        commercetools.Sdk.Api.Models.Orders.Order order = null;

        try
        {
            order = await _orderQueryService.GetOrder(cmd.OrderNumber);
        }
        catch (NotFoundException)
        {
            throw new OrderNotFoundException($"Order number {cmd.OrderNumber} not found");
        }

        if (order == null)
        {
            throw new OrderNotFoundException($"Order number {cmd.OrderNumber} not found");
        }
        //TagTransaction("OrderReference", order.Id);
        TagTransaction(Agent.Tracer.CurrentTransaction, "OrderReference", order.Id);

        bool statusIsOk = false;
        var floristStatus = OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS, order?.Custom?.Fields ?? null);
        foreach (var statusNeeded in orderAction.PreviousStatusNeeded)
        {
            if (statusNeeded.Equals(floristStatus))
            {
                statusIsOk = true;
                break;
            }
        }

        if (!statusIsOk)
        {
            var possibleStatus = "";
            orderAction.PreviousStatusNeeded.ForEach(status =>
            {
                possibleStatus += ", " + status;
            });

            throw new OrderStatusNotConsistentException($"Action {cmd.Action} on order number {cmd.OrderNumber} not possible with status {floristStatus}. Possible status : {possibleStatus}");
        }

        OrderNewHistoryRecordMessageBuilder historyBuilder = new OrderNewHistoryRecordMessageBuilder();
        historyBuilder.AddCommerceToolsID(order.Id)
            .AddOrderNumber(order.OrderNumber)
            .AddInitialOrderStatus(order.GetFloristOrderStatus())
            .AddExecutingFloristId(order.GetExecutingFloristId())
            .AddOrderAmount(order.GetTotalItemsPrice() + order.GetDeliveryPrice())
            .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(cmd))
            .AddOrderAction(orderAction.NewStatus)
        .AddMessage($"Pfs asked to update order {order.OrderNumber} to {orderAction.NewStatus}");
        //.AddCtOrderPreUpdate(order?.Serialize(SerializerType.CommerceTools, _serializerService));
        await _orderLogHistoryKafkaPublisherHelper.Publish(historyBuilder.Build(), _kafkaTopicsSettings.CurrentValue.Order);

        OrderCheckRequest request = new OrderCheckRequest
        {
            FloristIdentifier = cmd.ExecutingFloristId,
            //OrderIdentifier = cmd.CommerceToolsOrderId,
            OrderIdentifier = order.Id,
            Reason = cmd.Reason ?? "nd",
            ReasonCode = cmd.ReasonCode ?? "nd",
            NewStatus = orderAction.NewStatus,
        };
        DateTime deliveryDate = DateTime.MinValue;
        string strDeliveryDate = null;

        if (order?.ShippingAddress?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.ShippingAddress.DATE) ?? false)
            strDeliveryDate = order.ShippingAddress.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.DATE].ToString();

        if (DateTime.TryParseExact(strDeliveryDate, "yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out deliveryDate))
            request.DeliveryDate = deliveryDate.ToString();

        bool slaveEndpointCalled = false;

        if (_ctOrderWrapperSettings?.CurrentValue?.CountryCode.Equals("FR", StringComparison.OrdinalIgnoreCase) ?? false)
        {
            string newStatus = cmd.Action switch
            {
                "ABSENT" => "DA",
                "ASSIGNED" => "AFF",
                "REASSIGN" => "AFR",
                "CANCELLED" => "ANN",
                "DELIVERED" => "LFM",
                _ => string.Empty,
            };

            var dto = new UpdateOrderStatusRAODTO { ReasonCode = cmd.ReasonCode ?? string.Empty, ExecutingFloristId = cmd.ExecutingFloristId };
            var response = await _RAOSupplierHttpService.UpdateOrderStatus(cmd.OrderNumber, newStatus, dto);
            if (!response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                throw new Exception(content);
            }
        }
        else
        {
            var result = await _backendService.OrderCheck(request);
            if (result.Status != null && result.Status.ToUpper() != "OK")
            {
                result = await CheckAndCallSlaveEndpoint(request);
                slaveEndpointCalled = true;
                if (result.Status != null && result.Status.ToUpper() != "OK")
                {
                    throw new LegacyBackendExceptions.LegacyBackendInfrastructureException(result.Status);
                }
            }
        }


        // /!\ This second call getOrder is needed to avoid a 409 CT response
        try
        {
            order = await _orderQueryService.GetOrder(cmd.OrderNumber);
        }
        catch (NotFoundException)
        {
            throw new OrderNotFoundException($"Order number {cmd.OrderNumber} not found");
        }
        string parameters = (string.IsNullOrWhiteSpace(cmd.OrderNumber) ? "null" : cmd.OrderNumber) +
                "," + (string.IsNullOrWhiteSpace(cmd.CommerceToolsOrderId) ? "null" : cmd.CommerceToolsOrderId) +
                "," + (string.IsNullOrWhiteSpace(cmd.ExecutingFloristId) ? "null" : cmd.ExecutingFloristId) +
                "," + (string.IsNullOrWhiteSpace(cmd.Action) ? "null" : cmd.Action);
    
        try
        {
            await _orderUpdateService.UpdateCustomField(order.Id, order.Version, order.OrderNumber, CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS, orderAction.NewStatus, false);
            if (orderAction.ResetExecutingState)
            {
                await _orderUpdateService.RefuseOrder(order.Id, order.Version, order.OrderNumber);
            }
        }
        catch (ConcurrentModificationException ex)
        {
            Thread.Sleep(200);
            order = await _orderQueryService.GetOrder(cmd.OrderNumber);
            await _orderUpdateService.UpdateCustomField(order.Id, order.Version, order.OrderNumber, CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS, orderAction.NewStatus, false);
            if (orderAction.ResetExecutingState)
            {
                await _orderUpdateService.RefuseOrder(order.Id, order.Version, order.OrderNumber);
            }
            _logger.LogError("Error Exception {exception} on OrderActionsFacade.UpdateStatusAsync({parameters}) - Message: {ex.Message} -  ::: StackTrace: {ex.StackTrace}", ex.GetType().Name, parameters, ex.Message, ex.StackTrace);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error Exception {exception} on OrderActionsFacade.UpdateStatusAsync({parameters}) - Message: {ex.Message} ::: StackTrace: {ex.StackTrace}", ex.GetType().Name, parameters, ex.Message, ex.StackTrace);
        }

        OrderActionLogModel orderActionLogModel = new OrderActionLogModel();
        orderActionLogModel.ExecutingFloristId = OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, order?.Custom?.Fields ?? null);
        orderActionLogModel.OrderNumber = order.OrderNumber;
        orderActionLogModel.CommerceToolsID = order.Id;
        orderActionLogModel.OrderAction = cmd.Action;
        orderActionLogModel.LogDate = DateTime.Now;
        orderActionLogModel.InitialOrderStatus = OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS, order?.Custom?.Fields ?? null);
        orderActionLogModel.OrderAmount = OrderExtensionMethods.GetTotalItemsPrice(order) + OrderExtensionMethods.GetDeliveryPrice(order);
        orderActionLogModel.Id = Guid.NewGuid().ToString();
        await _orderActionLogRepository.InsertOneAsync(orderActionLogModel);


        historyBuilder.AddCommerceToolsID(cmd.CommerceToolsOrderId)
            .AddOrderNumber(order.OrderNumber)
            .AddInitialOrderStatus(order.GetFloristOrderStatus())
            .AddExecutingFloristId(order.GetExecutingFloristId())
            .AddOrderAmount(order.GetTotalItemsPrice() + order.GetDeliveryPrice())
            .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(cmd))
            .AddOrderAction(orderAction.NewStatus + " DONE")
        .AddMessage($"Pfs has updated order {order.OrderNumber} to {orderAction.NewStatus}");
        //.AddCtOrderPreUpdate(order?.Serialize(SerializerType.CommerceTools, _serializerService));
        await _orderLogHistoryKafkaPublisherHelper.Publish(historyBuilder.Build(), _kafkaTopicsSettings.CurrentValue.Order);

        if(!slaveEndpointCalled && (!_ctOrderWrapperSettings?.CurrentValue?.CountryCode.Equals("FR", StringComparison.OrdinalIgnoreCase) ?? false))
            await CheckAndCallSlaveEndpoint(request);

        return order.OrderNumber;

    }

    public async Task<string> InsertNoteAsync(Command.V1.InsertNote cmd)
    {
        try
        {
            _logger.LogInformation($"Received {Newtonsoft.Json.JsonConvert.SerializeObject(cmd)}");
        }
        catch { }


        if(cmd.OrderNoteDto.Content.Length > 255)
            throw new InvalidNoteContentLengthException($"Content note limited at 255 caracters");


        var order = await _orderService.GetById(cmd.OrderNoteDto.OrderId);
        
        if(order == null)
            throw new OrderNotFoundException($"Order id {cmd.OrderNoteDto.OrderId} not found");
        

        var note = new GlobalOrderNoteModel {Id = Guid.NewGuid().ToString(), Content = cmd.OrderNoteDto.Content, OrderId = cmd.OrderNoteDto.OrderId, CreatedAt = DateTime.Now, Owner = cmd.OrderNoteDto.Owner, Category = cmd.OrderNoteDto.Category };

        await _orderNoteRepository.InsertOneAsync(note);


        return order.OrderNumber;

    }

    public async Task<string> UpdateNoteAsync(Command.V1.UpdateNote cmd)
    {
        try
        {
            _logger.LogInformation($"Received {Newtonsoft.Json.JsonConvert.SerializeObject(cmd)}");
        }
        catch { }

        if (cmd.OrderNoteDto.Content.Length > 255)
            throw new InvalidNoteContentLengthException($"Content note limited at 255 caracters");

        var note = await _orderNoteRepository.FindByIdAsync(cmd.OrderNoteDto.Id);

        if(note == null)
            throw new NoteNotFoundException($"Note {cmd.OrderNoteDto.Id} not found");

        note.Category = cmd.OrderNoteDto.Category;
        note.Content = cmd.OrderNoteDto.Content;
        note.OrderId = cmd.OrderNoteDto.OrderId;
        note.Owner = cmd.OrderNoteDto.Owner;
        await _orderNoteRepository.ReplaceOneAsync(note);
        return cmd.OrderNoteDto.Id;

    }

    
    private async Task<OrderCheckRequestResponse> CheckAndCallSlaveEndpoint(OrderCheckRequest request)
    {
        OrderCheckRequestResponse result = new OrderCheckRequestResponse();
        try
        {
            var sectionExists = _configuration.GetChildren().Any(item => item.Key == "LegacyBackendSlaveEndpoint");
            if (sectionExists && _serviceProvider != null)
            {
                var backendSlaveService = _serviceProvider.GetService<ILegacyBackendSlaveService>();

                result = await backendSlaveService.OrderCheck(request);
                if (result.Status != null)
                {
                    _logger.LogWarning($"Call to the slave endpoint for order {request.OrderIdentifier} failed because of {result.Status}");
                }
                else
                {
                    _logger.LogInformation($"Call to the slave endpoint for order {request.OrderIdentifier} successfully done");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning($"Error while calling the slave endpoint for order {request.OrderIdentifier}: {ex.Message}");
        }
        return result;
    }

    private OrderAction getOrderAction(string command)
    {
        List<OrderAction> orderActions = new List<OrderAction>();
        _configuration.GetSection("OrderActions").Bind(orderActions);

        foreach(var action in orderActions) { 
            if(action.Action.Equals(command))
            {
                return action;
            }
        }
        throw new IncorrectOrderActionException("Unkown order action provided");
    }
}
