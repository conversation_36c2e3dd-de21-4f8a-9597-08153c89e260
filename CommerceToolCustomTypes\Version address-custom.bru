meta {
  name: Version address-custom
  type: http
  seq: 3
}

get {
  url: https://api.europe-west1.gcp.commercetools.com/{{CTP_PROJECT_KEY}}/types/key=address-custom
  body: none
  auth: bearer
}

headers {
  Content-Type: application/json
}

auth:bearer {
  token: {{TOKEN}}
}

script:post-response {
  let body = res.getBody();
  let version = body.version;
  bru.setEnvVar("ADDRESS_CUSTOM_VERSION", version);
}
