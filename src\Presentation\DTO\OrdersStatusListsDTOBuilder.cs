﻿using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders.DTO;
using IT.SharedLibraries.CT.Orders.Services;
using ITF.SharedModels.Group.Enums;

namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class OrdersStatusListsDTOBuilder(IOrderSummaryDtoService orderSummaryDtoBuilderService)
{
    private OrdersStatusListsDTO ordersStatusListsDTO = new();
    private DateTime now;
    private DateTime moringMax;

    public void WithDateTimes(int morningMaxHour)
    {
        now = DateTime.Now;
        moringMax = now.Date.AddHours(morningMaxHour);
    }

    public async Task WithCTOrders(IList<IOrder> orders, string language, string floristId)
    {
        if (now == default || moringMax == default)
        {
            throw new ArgumentNullException("WithDateTimes methode must be called before WithCTOrders");
        }
        FloristTypeEnum floristType = FloristTypeEnum.Unknown;
        foreach (var order in orders)
        {
            
            if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, order?.Custom?.Fields ?? null) == floristId)
                floristType = FloristTypeEnum.Executor;

            if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID, order?.Custom?.Fields ?? null) == floristId)
                floristType = FloristTypeEnum.Transmittor;

            OrderSummaryDTO orderDTO = await orderSummaryDtoBuilderService.BuildFromCTOrder((commercetools.Sdk.Api.Models.Orders.Order)order, language, floristType);

            if (orderDTO.ExternalDeliveryServiceRequested)
                ordersStatusListsDTO.InDelivery.Add(orderDTO);
            else if (OrderCanBeLate(orderDTO) && IsDeliveryLate(now, orderDTO.DeliveryDate, moringMax, orderDTO.DeliveryMoment))
                ordersStatusListsDTO.Late.Add(orderDTO);
            
            else
            {
                switch (orderDTO.Status)
                {
                    case CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ASSIGNED:
                        ordersStatusListsDTO.ToAccept.Add(orderDTO);
                        break;
                    case CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ACCEPTED:
                        ordersStatusListsDTO.ToPrepare.Add(orderDTO);
                        break;
                    
                    /*case CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_EXTERNAL_COURRIER:
                        ordersStatusListsDTO.DeliveryCourierRequested.Add(orderDTO);
                        break;*/
                    case CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ABSENT:
                        ordersStatusListsDTO.Absent.Add(orderDTO);
                        break;
                    default:
                        // Unkown status code {Status} for order {OrderId}
                        ordersStatusListsDTO.Other.Add(orderDTO);
                        break;
                }
            }

            if (OrderCanByInProgress(orderDTO) && orderDTO.InProgress)
            {
                ordersStatusListsDTO.InProgress.Add(orderDTO);
            }
        }
    }

    public OrdersStatusListsDTO Build()
    {
        OrdersStatusListsDTO result = ordersStatusListsDTO;
        ordersStatusListsDTO = new();
        return result;
    }

    private bool OrderCanBeLate(OrderSummaryDTO orderDTO)
    {
        if (!orderDTO.Status.Equals(CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ASSIGNED)
            && !orderDTO.Status.Equals(CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ABSENT)
            && (orderDTO.Status.Equals(CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ACCEPTED) || orderDTO.InProgress))
        {
            return true;
        }
        return false;
    }

    private bool OrderCanByInProgress(OrderSummaryDTO orderDTO)
    {
        if (orderDTO.Status.Equals(CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ACCEPTED) || orderDTO.Status.Equals(CtOrderCustomAttributesNames.Order.ORDER_STATUS_CODE_ABSENT))
        {
            return true;
        }
        return false;
    }

    private static bool IsDeliveryLate(DateTime now, DateTime deliveryDate, DateTime morningMax, string deliveryWindow)
    {
        if (now.Date.CompareTo(deliveryDate.Date) > 0)
            return true;

        //to keep for an upcomming ticket
        //if (IsSameDay(now, deliveryDate))
        //{
        //    if (deliveryWindow.Equals(CtOrderCustomAttributesNames.Order.MORNING_DELIVERY_WINDOW_CODE) && now.CompareTo(morningMax) > 0)
        //        return true;
        //}

        return false;
    }
    

    private static bool IsSameDay(DateTime date1, DateTime date2)
    {
        if (date1.Day.CompareTo(date2.Day) == 0)
            return true;

        return false;
    }

    private static OrderSummaryDTO CopyOrderSummaryDTO(OrderSummaryDTO source)
    {
        OrderSummaryDTO dto = new OrderSummaryDTO();
        dto.CTId = source.CTId;
        dto.CTVersion = source.CTVersion;
        dto.DeliveryDate = source.DeliveryDate;
        dto.DeliveryMoment = source.DeliveryMoment;
        dto.ExternalCourierRequested = source.ExternalCourierRequested;
        dto.InProgress = source.InProgress;
        dto.Invoice = source.Invoice;
        dto.Name = source.Name;
        dto.New = source.New;
        dto.OrderId = source.OrderId;
        dto.OrderDate = source.OrderDate;
        dto.Price = source.Price;
        dto.ProductCode = source.ProductCode;
        dto.ProductImage = source.ProductImage;
        dto.Status = source.Status;
        dto.Sequencing = source.Sequencing;
        dto.Town = source.Town;

        return dto;
    }
}
