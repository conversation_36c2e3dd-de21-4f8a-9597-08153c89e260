meta {
  name: Add line item types composition
  type: http
  seq: 10
}

post {
  url: https://api.europe-west1.gcp.commercetools.com/{{CTP_PROJECT_KEY}}/types/key=line-item-custom
  body: json
  auth: bearer
}

auth:bearer {
  token: {{TOKEN}}
}

body:json {
  {
    "actions": [
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {"type": {"name": "String"},"name": "composition","label": {"en": "Composition"},"required": false}
      }
    ],
    "version": {{version}}
  }
  
    
  
}
