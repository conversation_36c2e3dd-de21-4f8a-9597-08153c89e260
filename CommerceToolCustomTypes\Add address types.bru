meta {
  name: Add address types
  type: http
  seq: 5
}

post {
  url: https://api.europe-west1.gcp.commercetools.com/{{CTP_PROJECT_KEY}}/types/key=address-custom
  body: json
  auth: bearer
}

auth:bearer {
  token: {{TOKEN}}
}

body:json {
  {
    "actions": [
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {"type": {"name": "Number"},"name": "longitude","label": {"en": "address longitude"},"required": false }
      },
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {"type": {"name": "Number"},"name": "latitude","label": {"en": "address latitude"},"required": false }
      }
    ],
    "version": {{ADDRESS_CUSTOM_VERSION}}
  }
}
