###
# Values for recette environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/IT.Microservices.CT.Order.Wrapper/"
#helm diff upgrade itctorderwrapper ${helmChartPath} --values chart-values/values-recette-italy.yaml -n iti-ms --set 'image.tag=latest,image.repository=itirecetteacr.azurecr.io/itctorderwrapper'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "itctorderwrapper"
fullnameOverride: "itctorderwrapper"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "iti-ms-itctorderwrapper"

dotnetProgramName: "IT.Microservices.CT.Order.Wrapper.dll"

podAnnotations:
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/itctorderwrapper.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"  
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "iti-microservices"
  # vault.hashicorp.com/agent-inject-secret-itctorderwrapper.pass: "applications/iti-microservices"

  # Inject secret via a configmap named itctorderwrapper-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'itctorderwrapper-secrets'
  
  #inject secret via env variables 
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  vault.hashicorp.com/role: 'iti-microservices'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/iti-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/iti-microservices" -}}
      export Client__ClientId={{ .Data.iti_microservice_write_id }}
      export Client__ClientSecret={{ .Data.iti_microservice_write_password }}
      export LegacyBackendSlaveEndpoint__Authentication__Credentials__username={{ .Data.iti_backend_api_user }}
      export LegacyBackendSlaveEndpoint__Authentication__Credentials__password={{ .Data.iti_backend_api_password }}
      export LegacyBackendEndpoint__Authentication__Credentials__client_id={{ .Data.iti2_backend_api_user }}
      export LegacyBackendEndpoint__Authentication__Credentials__client_secret={{ .Data.iti2_backend_api_password }}
      export SlackAlert__ApiToken={{ .Data.slack_webhook }}
    {{- end }}

service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.recette.interflora.it
  path: "/itctorderwrapper"
  tls:
  - hosts:
    - microservices.recette.interflora.it
    secretName: "recette-interflora-it-cert"  
  enabled: true
  ingressClass: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "http://localhost:3000,https://*.interflora.it,https://*.recette.interflora.it"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow *************/32;
      allow *************/32;
      allow ***************/32;
      allow **************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/itctorderwrapper$ /itctorderwrapper/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

livenessProbe:
  httpGet:
    path: /health
    port: 80
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 100

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 

  CTOrderWrapperSettings: |-
    "CTOrderWrapperSettings": {
      "CountryCode" : "IT2",
      "PFsGetOrderDocumentUrlFormat": "http://itmsdocuments.iti-ms/itmsdocuments/api/v1/GetOrderDoc?orderId={orderIdentifier}&floristId={floristIdentifier}&type={type}"
    }

  CommercetoolClient: |-
    "Client": {
      "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
      "ProjectKey": "interfloratest", // replace with your project key
      "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/"
    }

  CommerceToolCustomSettings: |-
    "CommerceToolCustomSettings": {
      "LocalCountryCode": "IT",
      "LocalCountryChannelKey": "interflora.it",
      "LocalCountryStoreKey": "ITI",
      "CtMoruningProductTypeKey": "mourning",
      "LocalCountryAccessoriesCategoryKey": "iti-ite-itp-general-accessories",
      "LocalCountryProductsCategoryKey": "iti-ite-itp-products",
      "OutboundOrderShippingMethodKey": "pfs-international-italy",
      "MourningShippingMethodKey": "mourning",
      "PfsShippingMethodKey": "pfs",
      "ProductKeysNotToBeDisplayed": []
    }

  SlackAlertConfig: |-
    "SlackAlert": {
      "DefaultChannel": "alerts-ms-it-recette",
      "BotName": "Error Alert Bot",
      "MaxRetryAttempts": 3
    }

  LegacyBackendEndpoint: |-
   "LegacyBackendEndpoint": {
      "Authentication": {
        "Credentials": {
          "grant_type": "client_credentials",
          "resource": "https://intf-test.sandbox.operations.eu.dynamics.com",
        },
        "URL": "https://login.windows.net/interflora.es/oauth2/token",
        "AuthMethod": "OAUTH",
        "UseExpirationTime": true
      },
      "Url": "https://intf-test.sandbox.operations.eu.dynamics.com/",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  LegacyBackendEndpointList: |-
    "LegacyBackendEndpointList": {
      "OrderCheckUpdate": {
        "Action": "POST",
        "Endpoint": "/api/services/EQMPFS/EQMPFSCheckOrderStatusUpdate/checkOrderStatusUpdateITI"
      }
    }

  LegacyBackendSlaveEndpoint: |-
   "LegacyBackendSlaveEndpoint": {
      "Authentication": {
        "Credentials": {
        },
        "URL": "https://dev-api.interflora.it/api/Token",
        "AuthMethod": "JWT",
        "UseExpirationTime": true
      },
      "Url": "https://dev-api.interflora.it/api/",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  LegacyBackendSlaveEndpointList: |-
    "LegacyBackendSlaveEndpointList": {
      "OrderCheckUpdate": {
        "Action": "POST",
        "Endpoint": "v1/pfs/order/check"
      }
    }

  sequenceGeneratorEndpointConfig: |-
    "SequenceGeneratorGetNextEndpoint": {
      "Url": "http://itsequencegenerator.iti-ms:80/itsequencegenerator/api/v1",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  Kafka: |-
    "Kafka": {
      "TopicsToCreateConfigurations": [
        {
          "TopicName": "order",
          "NumberOfPartitions": 20,
          "RetentionMs": 7200000 //2h
        }
      ]
    }

  KafkaTopicsSettings: |-
    "KafkaTopicsSettings": {
      "Order": "order"
    }

  spanishOrderSettings: |-
    "SpanishOrderSettings": {
      "ITFPLUSproductKey": "ITFPLUS",
      "VatKey": "vat",
      "VatReducedKey": "vat-reduced",
      "PfsShippingMethodKey": "pfs",
      "PfsReducedShippingMethodKey": "pfs-reduced",
      "PfsMourningReducedShippingMethodKey": "reduced-mourning",
      "ProductsHiddenToSenderFlorist": [],
      "CityStates": [],
      "States": [],
      "Provinces": []
    }

  OrderConfirmationSettings: |-
    "OrderConfirmationSettings": {
      "Base64Key_256bits": "*******************************************=",
      "Base64Iv_128bits": "sA9mD2QeD7h/NFwEaRK/wA=="
    }

serilogConfig: |-       
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "iti-itctorderwrapper"
    }
  }

env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "recette"

  - name: ASPNETCORE_COUNTRY
    value: "it"

  - name: MongoDb__DatabaseName
    value: "it-florist"
    
  - name: MongoDb__ConnectionString
    value: "mongodb://iti-ms-mongodb-0.iti-ms-mongodb-headless.iti-ms-common.svc.cluster.local:27017,iti-ms-mongodb-1.iti-ms-mongodb-headless.iti-ms-common.svc.cluster.local:27017,iti-ms-mongodb-2.iti-ms-mongodb-headless.iti-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: ElasticApm__Environment
    value: "iti-recette"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "iti-itctorderwrapper"    

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "iti-ms-kafka-headless.iti-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "iti-ms-redis-headless.iti-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://iti-unleash.iti-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "iti-itctorderwrapper"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "development"