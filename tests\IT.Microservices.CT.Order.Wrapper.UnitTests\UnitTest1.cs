using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using IT.Microservices.CT.Order.Wrapper.Presentation;
using System.Collections.Generic;
using Xunit;
using System;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Types;
using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.CustomAttributes;

namespace IT.Microservices.CT.Order.Wrapper.UnitTests
{
    public class UnitTest1
    {
        [Fact]
        public void Test1()
        {
            DateTime late = DateTime.Now.AddDays(-2);
            DateTime today = DateTime.Now;
            DateTime tomorrow = DateTime.Now.AddDays(1);
            DateTime later = DateTime.Now.AddDays(2);

            List<commercetools.Sdk.Api.Models.Orders.IOrder> orders = new();
            commercetools.Sdk.Api.Models.Orders.Order order = new() { ShippingAddress = new Address { Custom = new CustomFields() } };
            order.ShippingAddress.Custom.Fields = new FieldContainer
            {
                { CtOrderCustomAttributesNames.ShippingAddress.DATE, late.ToString() }
            };
            orders.Add(order);

            order = new() { ShippingAddress = new Address { Custom = new CustomFields() } };
            order.ShippingAddress.Custom.Fields = new FieldContainer
            {
                { CtOrderCustomAttributesNames.ShippingAddress.DATE, today.ToString() }
            };
            orders.Add(order);

            order = new() { ShippingAddress = new Address { Custom = new CustomFields() } };
            order.ShippingAddress.Custom.Fields = new FieldContainer
            {
                { CtOrderCustomAttributesNames.ShippingAddress.DATE, today.ToString() }
            };
            orders.Add(order);

            order = new() { ShippingAddress = new Address { Custom = new CustomFields() } };
            order.ShippingAddress.Custom.Fields = new FieldContainer
            {
                { CtOrderCustomAttributesNames.ShippingAddress.DATE, tomorrow.ToString() }
            };
            orders.Add(order);

            /*order = new() { ShippingAddress = new Address { Custom = new CustomFields() } };
            order.ShippingAddress.Custom.Fields = new FieldContainer
            {
                { CtOrderCustomAttributesNames.ShippingAddress.DATE, later.ToString() }
            };
            orders.Add(order);
            */

            OrdersCountDTOBuilder ordersCountDTOBuilder = new();

            ordersCountDTOBuilder.WithCTOrders(orders);
            OrdersCountDTO dto = ordersCountDTOBuilder.Build();

            Assert.NotNull(dto);
            Assert.Equal(1, dto.Late);
            Assert.Equal(2, dto.Today);
            Assert.Equal(1, dto.Tomorrow);
            Assert.Equal(0, dto.Later);
        }
    }
}
