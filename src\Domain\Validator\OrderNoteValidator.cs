﻿using FluentValidation;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

namespace IT.Microservices.CT.Order.Wrapper.Domain.Validator;

public class OrderNoteValidator : AbstractValidator<OrderNoteDto>
{
    public OrderNoteValidator()
    {
        RuleFor(o => o.Owner).NotEmpty();
        RuleFor(o => o.OrderId).NotEmpty();
        RuleFor(o => o.Content).NotEmpty();
        RuleFor(o => o.Category).NotEmpty();
    }
}
