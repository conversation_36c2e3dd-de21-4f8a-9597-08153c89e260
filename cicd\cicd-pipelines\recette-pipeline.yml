trigger:
- master

pool:
  vmImage: ubuntu-latest

variables:
  #####################################
  ## common-variables
  #####################################

  projectName: 'itctorderwrapper'

  #Use by csproj files to build and run unit tests
  dotnetProjectName: 'IT.Microservices.CT.Order.Wrapper'
  dotnetTestName: 'IT.Microservices.CT.Order.Wrapper.UnitTests'

  helmVersion: 3.7.1
  HELM_EXPERIMENTAL_OCI: '1'

  #Must match chart/Chart.yaml name
  helmChartName: 'itctorderwrapper'

  #Helm Release name 
  helmReleaseName: 'itctorderwrapper'

  #Use Azure devOps build id to tag image --> must be remplaced by commitID 
  imageTag: $(build.SourceVersion)

  #Registry name to store docker image and helm chart (create if not exist, must be lowercase)
  imageRepository: 'itctorderwrapper'

  #####################################
  ##recette-variables
  #####################################

  #According to chart/Chart.yaml value   
  helmChartVersion: "1.4.6"
  
  containerRegistry: 'itfrecetteacr.azurecr.io'
  containerFullPath: '$(containerRegistry)/$(imageRepository)'
  containerRegistryLogin: 'itfrecetteacr'
  #containerRegistryPwd: 'must be defined in azure pipeline variables'
    
  #Azure service connection name(Project settings -> Service connection -> Azure Resource Manager)
  Azure.ServiceConnection: 'it-recette-cicd-sp'

  Azure.resourceGroup: 'itf-recette-k8s-rg'
  Azure.kubernetesClusterName: 'itf-recette-k8s-aks'

  #Shared Helm chart repository name (output from terraform)
  HelmChartRepository: 'itfsharedacr'
  HelmChartRepositoryClientID: "70085f74-d6cc-4bda-8cd1-bad624af5a20"
  #HelmChartRepositoryClientSecret: 'must be defined in azure pipeline variables'
  
  #Enable Azure devops debug mode
  System.Debug: 'false'

stages:
- stage: Build_Stage
  displayName: Build image
  jobs:  
  - job: Build_Job
    displayName: Build and push Docker/Helm image
    steps: 
    - template: templates/build.yml

- stage: Deploy_France
  dependsOn: Build_Stage
  displayName: Deploy itctorderwrapper France (k8s)
  jobs: 
  - deployment: Deploy_France
    displayName: Deploy France app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-recette-france.yaml"
      K8S.Namespace: 'itf-ms'
    environment: recette-itf-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Italy
  dependsOn: Build_Stage
  displayName: Deploy itctorderwrapper Italy (k8s)
  jobs: 
  - deployment: Deploy_Italy
    displayName: Deploy Italy app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-recette-italy.yaml"
      K8S.Namespace: 'iti-ms'
    environment: recette-iti-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Spain
  dependsOn: Build_Stage
  displayName: Deploy availability Spain (k8s)
  jobs: 
  - deployment: Deploy_Spain
    displayName: Deploy Spain app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-recette-spain.yaml"
      K8S.Namespace: 'ite-ms'
    environment: recette-ite-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Portugal
  dependsOn: Build_Stage
  displayName: Deploy availability Portugal (k8s)
  jobs: 
  - deployment: Deploy_Portugal
    displayName: Deploy Portugal app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-recette-portugal.yaml"
      K8S.Namespace: 'itp-ms'
    environment: recette-itp-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

# - stage: Deploy_Italy2
#   dependsOn: Build_Stage
#   displayName: Deploy itctorderwrapper Italy 2 (k8s)
#   jobs:
#     - deployment: Deploy_Italy2
#       displayName: Deploy Italy 2 app
#       variables:
#         commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
#         chartValuesFile: "./cicd/chart-values/values-recette-italy2.yaml"
#         K8S.Namespace: 'iti2-ms'
#       environment: recette-iti-$(projectName)
#       strategy:
#         runOnce:
#           deploy:
#             steps:
#               - checkout: self
#               - template: templates/deploy.yml