﻿using FluentValidation;
using IT.SharedLibraries.CT.Settings;
using ITF.SharedModels.DataModels.Order;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace IT.Microservices.CT.Order.Wrapper.Domain.Validator;

public class GlobalOrderValidator : AbstractValidator<GlobalOrderModel>
{
    public GlobalOrderValidator(IOptionsMonitor<CommerceToolCustomSettings> commonSettings)
    {
        RuleFor(o => o.DeliveryCountryCode).NotEmpty().Length(2);
        RuleFor(o => o.SenderCountryCode).NotEmpty().Length(2);
        RuleFor(o => o.SenderFloristIdentifier).NotEmpty();
        RuleFor(o => o.CurrencyCode).NotEmpty();

        RuleFor(b => b.Billing.LastName).NotEmpty();
        RuleFor(b => b.Billing.FirstName).NotEmpty();
        RuleFor(b => b.Billing.CountryCode).NotEmpty();

        RuleFor(s => s.Shipping.StreetName).NotEmpty();

        When(s => s.Shipping.LastName.IsNullOrEmpty(), () => {
            RuleFor(s => s.ContactFirstName).NotEmpty();
            RuleFor(s => s.ContactLastName).NotEmpty();
        }).Otherwise(() => {
            RuleFor(s => s.Shipping.FirstName).NotEmpty();
            RuleFor(s => s.Shipping.LastName).NotEmpty();
        });

        var spnishCountryCodes = new[] { "ES", "PT", "IT" };
        if (commonSettings?.CurrentValue != null && commonSettings?.CurrentValue?.LocalCountryCode != null
            && !spnishCountryCodes.Contains(commonSettings?.CurrentValue?.LocalCountryCode))
        {
            // for spain and portugal zipcode must not be mandatory
            RuleFor(s => s.Shipping.ZipCode).NotEmpty();
        }
        RuleFor(s => s.Shipping.City).NotEmpty();
        RuleFor(s => s.Shipping.CountryCode).NotEmpty();
        RuleFor(s => s.Shipping.DeliveryDate).NotEmpty();

        RuleFor(p => p.Products).NotEmpty();
    }
}
