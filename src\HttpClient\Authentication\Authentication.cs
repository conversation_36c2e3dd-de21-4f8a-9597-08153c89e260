﻿namespace ITF.SharedLibraries.HttpClient.Authentication
{
  public class Authentication
  {
    public string AuthMethod { get; set; }
    public bool UseExpirationTime { get; set; }
    public string Token { get; set; }
    public string URL { get; set; }
    public Credentials.Credentials Credentials { get; set; }
    public string QueryParam { get; set; }
  }

  public enum AuthMethod
  {
    JWT_KEY,
    BASIC_KEY,
    OAUTH,
    JWT,
    SOAP,
    XWSSE,
    FRESH_PORTAL_JWT,
    QUERY_PARAM,
    BEARER
  }
}
