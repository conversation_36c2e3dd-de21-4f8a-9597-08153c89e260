meta {
  name: Token
  type: http
  seq: 1
}

post {
  url: https://auth.europe-west1.gcp.commercetools.com/oauth/token
  body: formUrlEncoded
  auth: basic
}

auth:basic {
  username: {{CTP_CLIENT_ID}}
  password: {{CTP_CLIENT_SECRET}}
}

body:form-urlencoded {
  grant_type: client_credentials
  scope: {{SCOPE}}:{{CTP_PROJECT_KEY}}
}

script:post-response {
  let body = res.getBody();
  let token = body.access_token;
  bru.setEnvVar("TOKEN", token);
}
