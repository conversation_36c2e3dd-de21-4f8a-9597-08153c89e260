﻿using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Models.Orders;
using IT.Microservices.CT.Order.Wrapper.Application;
using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.Orders.DTO;
using IT.SharedLibraries.CT.Orders.Services;
using ITF.SharedModels.DataModels.Globals;
using ITF.SharedModels.Group.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace IT.Microservices.CT.Order.Wrapper.Presentation;

public class QueryOrderController(IOrderQueryService orderQueryService,
    ILogger<QueryOrderController> logger,
    IOrderUpdateService orderUpdateService,
    IOrderDtoService orderDtoService,
    IOrderSummaryDtoService orderSummaryDtoService,
    IFloristRepository floristRepository,
    IOrderNotificationRepository orderNotificationRepository,
    IOrderNoteRepository orderNoteRepository,
    IOptionsMonitor<CTOrderWrapperSettings> orderSettings,
    IOrderQueryFacade orderQueryFacade) : BaseController
{
    private readonly CTOrderWrapperSettings _orderSettings = orderSettings.CurrentValue;
    private const int MORNING_MAX_HOUR = 11;
    private const int TO_EXECUTE_ORDERS_LIMIT = 500;

    [HttpGet("GetById")]
    public async Task<IActionResult> GetById(string orderId, string floristId)
    {
        OrderDTO orderDto = null;
        commercetools.Sdk.Api.Models.Orders.Order order;
        try
        {
            order = await orderQueryService.GetOrder(orderId);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.Message);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }

        if (order == null)
        {
            return NotFound();
        }
        FloristTypeEnum floristType = FloristTypeEnum.Unknown;

        if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, order?.Custom?.Fields ?? null) == floristId)
            floristType = FloristTypeEnum.Executor;

        if(OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID, order?.Custom?.Fields ?? null) == floristId)
            floristType = FloristTypeEnum.Transmittor;

        if (floristType == FloristTypeEnum.Unknown)
            return Unauthorized();

        orderDto = await orderDtoService.BuildFromCTOrder(order, _orderSettings.Language, floristType);
        await orderUpdateService.UpdateReadByExecutingFloristCustomField(orderId, order.Version, order.OrderNumber, floristId, OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID, order?.Custom?.Fields ?? null));

        switch (floristType)
        {
            case FloristTypeEnum.Executor:
                if (!string.IsNullOrWhiteSpace(orderDto.TransmitterFloristId))
                {
                    var transmitter = await floristRepository.GetById(orderDto.TransmitterFloristId);
                    orderDto.TransmitterFlorist.Name = transmitter?.ShopLocation.Name;
                    orderDto.TransmitterFlorist.Contacts = transmitter?.Contacts.Where(c => c.Type == ContactTypeEnum.SecondEmail || c.Type == ContactTypeEnum.Email || c.Type == ContactTypeEnum.Phone).Select(c => new ContactDTO { Type = c.Type, Entry = c.Entry }).ToList();
                }
                break;
            case FloristTypeEnum.Transmittor:
                if (!string.IsNullOrWhiteSpace(orderDto.ExecutingFloristId))
                {
                    var executor = await floristRepository.GetById(orderDto.ExecutingFloristId);
                    orderDto.ExecutingFlorist.Name = executor?.ShopLocation.Name;
                    orderDto.ExecutingFlorist.Contacts = executor?.Contacts.Where(c => c.Type == ContactTypeEnum.SecondEmail || c.Type == ContactTypeEnum.Email || c.Type == ContactTypeEnum.Phone).Select(c => new ContactDTO { Type = c.Type, Entry = c.Entry }).ToList();
                }
                break;
        }
        var lastOrderNotification = orderNotificationRepository.GetLastModifiedNotificationByOrderId(order.Id).Result;
        //var t = _orderNotificationRepository.GetAll();
        orderDto.ModifiedFields = lastOrderNotification?.ModifiedFields;
        orderDto.Delivery.Region = order?.ShippingAddress?.Region ?? string.Empty;
        orderDto.Delivery.AdditionalStreetInfo = order?.ShippingAddress?.AdditionalStreetInfo ?? string.Empty;
        orderDto.Delivery.CompanyName = order?.ShippingAddress?.Company ?? string.Empty;
        orderDto.Delivery.ContactFirstName = OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.CONTACT_FIRST_NAME, order?.ShippingAddress?.Custom?.Fields ?? null);
        orderDto.Delivery.ContactLastName = OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.CONTACT_LAST_NAME, order?.ShippingAddress?.Custom?.Fields ?? null);

        return Ok(orderDto);
    }

    [HttpGet("GetToExecuteOrders")]
    public async Task<IActionResult> GetToExecuteOrders(string floristId)
    {
        OrderPagedQueryResponse orders = null;
        OrdersStatusListsDTO ordersStatusListsDTO = null;
        try
        {
            orders = await orderQueryFacade.GetToExecuteOrdersSorted(floristId, true, TO_EXECUTE_ORDERS_LIMIT);
            OrdersStatusListsDTOBuilder ordersStatusListsDTOBuilder = new(orderSummaryDtoService);
            ordersStatusListsDTOBuilder.WithDateTimes(MORNING_MAX_HOUR);
            await ordersStatusListsDTOBuilder.WithCTOrders(orders.Results, _orderSettings.Language, floristId);
            ordersStatusListsDTO = ordersStatusListsDTOBuilder.Build();
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, new StackTrace(e).GetFrame(0).GetMethod().Name + " " + e.Message, logger));
        }
        catch(Exception e) 
        {
            var s = new StackTrace(e);
            string callStack = "";
            foreach (var a in s.GetFrames().Select(f => f.GetMethod()))
                callStack += " - " + a.Name;
            return new BadRequestObjectResult(new ErrorObjectReponse(e.GetType().Name, e.Message + callStack, logger));
        }

        if (orders == null)
        {
            return NotFound();
        }
       
       

        return Ok(ordersStatusListsDTO);
    }

    [HttpGet("GetTransmittedOrders")]
    public async Task<IActionResult> GetTransmittedOrders(string floristId, int limit, int offset)
    {
        OrderPagedQueryResponse orders;
        try
        {
            orders = await orderQueryService.GetTransmittedOrders(floristId, limit, offset);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }

        if (orders == null)
        {
            return NotFound();
        }

        List<OrderSummaryDTO> transmissions = new();
        FloristTypeEnum floristType = FloristTypeEnum.Unknown;
        foreach(IOrder o in orders.Results)
        {
            if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, o?.Custom?.Fields ?? null) == floristId)
                floristType = FloristTypeEnum.Executor;

            if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID, o?.Custom?.Fields ?? null) == floristId)
                floristType = FloristTypeEnum.Transmittor;

            transmissions.Add(await orderSummaryDtoService.BuildFromCTOrder((commercetools.Sdk.Api.Models.Orders.Order) o, _orderSettings.Language, floristType));
        }

        return Ok(transmissions);
    }


    [HttpGet("GetOrdersHistory")]
    public async Task<IActionResult> GetOrdersHistory(string floristId, int limit, int offset)
    {
        if (string.IsNullOrEmpty(floristId))
        {
            return new BadRequestObjectResult(new
            {
                message = "floristId cannot be null or empty.",
            });
        }

        OrderPagedQueryResponse orders;
        try
        {
            orders = await orderQueryService.GetOrdersHistory(floristId, limit, offset);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }

        if (orders == null)
        {
            return NotFound();
        }
        FloristTypeEnum floristType = FloristTypeEnum.Unknown;
        List<OrderSummaryDTO> transmissions = [];

        foreach (IOrder o in orders.Results)
        {
           
            if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, o?.Custom?.Fields ?? null) == floristId)
                floristType = FloristTypeEnum.Executor;

            if (OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID, o?.Custom?.Fields ?? null) == floristId)
                floristType = FloristTypeEnum.Transmittor;
            transmissions.Add(await orderSummaryDtoService.BuildFromCTOrder((commercetools.Sdk.Api.Models.Orders.Order)o, _orderSettings.Language, floristType));
        }

        return Ok(transmissions);
    }

    [HttpGet("GetToExecuteOrdersCount")]
    public async Task<IActionResult> GetToExecuteOrdersCount(string floristId)
    {
        OrderPagedQueryResponse orders;
        try
        {
            orders = await orderQueryService.GetToExecuteOrders(floristId, false, TO_EXECUTE_ORDERS_LIMIT);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }

        if (orders == null)
        {
            return NotFound();
        }

        OrdersCountDTOBuilder ordersCountDTOBuilder = new();
        ordersCountDTOBuilder.WithCTOrders(orders.Results);
        OrdersCountDTO ordersCountDTO = ordersCountDTOBuilder.Build(); 

        return Ok(ordersCountDTO);
    }

    [HttpGet("GetOrderNotesList")]
    public async Task<IActionResult> GetOrderNotesList(string orderId)
    {  
        var notes = await orderNoteRepository.GetNoteByOrderId(orderId);
                   
        if (!notes.Any())
        {
            return NotFound();
        }

        return Ok(notes);
    }
}
