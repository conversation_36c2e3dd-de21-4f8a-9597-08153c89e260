﻿using IT.Microservices.CT.Order.Wrapper.Domain;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Notifications.Business.Legacy;
using MongoDB.Driver;

namespace IT.Microservices.CT.Order.Wrapper.Infrastructure;

public interface IOrderNotificationRepository : IMongoRepository<GlobalOrderNotification>
{
    Task<GlobalOrderNotification> GetLastModifiedNotificationByOrderId(string orderId);
}

public class OrderNotificationRepository(IMongoClient mongoClient) : MongoRepository<GlobalOrderNotification>(mongoClient, "order_notifications", "order-notification-subset"), IOrderNotificationRepository
{
    public async Task<GlobalOrderNotification> GetLastModifiedNotificationByOrderId(string orderId)
    {
        var notifications = await FilterByAsync(f => f.OrderId == orderId && f.Type.Equals(OrderNotificationType.MODIFICATION.ToString()));
        
        return notifications?.ToList().OrderByDescending(n => n.LastModified).FirstOrDefault();
    }
}
