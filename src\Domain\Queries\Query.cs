﻿using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace IT.Microservices.CT.Order.Wrapper.Domain.Queries;

public static class Query
{
    public static class V1
    {
        public class GetByKeyOrName
        {
            [Required]
            [SwaggerSchema("The product search string (key or name)")]
            public string Search { get; set; }
        }

        public class GetByKey
        {
            [Required]
            [SwaggerSchema("The product key")]
            public string Key { get; set; }
        }
    }
}
