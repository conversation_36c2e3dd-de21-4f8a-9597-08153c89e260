using ITF.SharedLibraries.HostBuilder.Extensions;

namespace IT.Microservices.CT.Order.Wrapper;

public static class Program
{
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args)
    {
        return CustomHostBuilder.CreateCustomHostBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
            });
    }
}
