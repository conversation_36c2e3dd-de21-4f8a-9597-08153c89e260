﻿namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class ProductPriceDTO : IEquatable<ProductPriceDTO>
{
    // the variant key
    public string VariantKey { get; set; }
    public decimal Price { get; set; }
    public string CustomerGroup { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidUntil { get; set; }

    public ProductPriceDTO(string variantKey, decimal price, string customerGroup, DateTime? validFrom, DateTime? validUntil)
    {
        VariantKey = variantKey;
        Price = price;
        CustomerGroup = customerGroup;
        ValidFrom = validFrom;
        ValidUntil = validUntil;
    }

    public bool Equals(ProductPriceDTO other)
    => (other.VariantKey == VariantKey &&
        other.Price == Price &&
        other.CustomerGroup == CustomerGroup && 
        other.ValidFrom == ValidFrom && 
        other.ValidUntil == ValidUntil);    
}