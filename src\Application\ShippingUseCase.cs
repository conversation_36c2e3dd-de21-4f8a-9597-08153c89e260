﻿using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ShippingMethods;
using IT.SharedLibraries.CT.Exceptions;
using IT.SharedLibraries.CT.Orders.Services;
using IT.SharedLibraries.CT.Products;
using IT.SharedLibraries.CT.ShippingMethods;

namespace IT.Microservices.CT.Order.Wrapper.Application;

public interface IShippingUseCase
{
    Task<decimal> GetDeliveryFeeForPfs(bool isMourning);
    Task<decimal> GetDeliveryFeeForPfs(List<string> productsKey, List<string> accessoriesKey, bool isMourning);
    Task<decimal> GetInternationalDeliveryFeeForPfs();
}
public class ShippingUseCase : IShippingUseCase
{
    private readonly IShippingMethodService _shippingMethodService;
    private readonly IUnitOrderService? _unitOrderService;
    private readonly IProductService _productService;
    private readonly ILogger<ShippingUseCase> _logger;

    public ShippingUseCase(IShippingMethodService shippingMethodService, IUnitOrderService unitOrderService, IProductService productService, ILogger<ShippingUseCase> logger)
    {
        _shippingMethodService = shippingMethodService;
        _unitOrderService = unitOrderService;
        _productService = productService;
        _logger = logger;
    }

    public async Task<decimal> GetDeliveryFeeForPfs(bool isMourning)
    {
        return await _shippingMethodService.GetDeliveryFeeForPfs(isMourning);
    }

    public async Task<decimal> GetDeliveryFeeForPfs(List<string> productsKey, List<string> accessoriesKey, bool isMourning)
    {
        List<IProduct> products = new(), accessories = new();

        List<string> expansions = new List<string> { "taxCategory" };
        foreach (string key in productsKey)
        {

            IProduct product = await _productService.GetByKey(key, expansions);
            if (product == null)
            {
                _logger.LogError($"Cannot find product with key {key}");
                throw new CtCustomException($"Cannot find product with key {key}");
            }
            else
            {
                products.Add(product);
                //_logger.LogInformation($"Product: {result.Product.Serialize(Serializer.SerializerType.CommerceTools, _serializerService)}");
            }
        }
        foreach (string key in accessoriesKey)
        {

            IProduct product = await _productService.GetByKey(key, expansions);
            if (product == null)
            {
                _logger.LogError($"Cannot find accessory with key {key}");
                throw new CtCustomException($"Cannot find accessory with key {key}");
            }
            else
            {
                accessories.Add(product);
                //_logger.LogInformation($"Product: {result.Product.Serialize(Serializer.SerializerType.CommerceTools, _serializerService)}");
            }
        }

        string shippingMethodKey = _unitOrderService.GetNationalShippingMethodKey(products, accessories, isMourning);
        IShippingMethod shippingMethod = await _shippingMethodService.GetByKey(shippingMethodKey);

        return await _shippingMethodService.GetDeliveryFeeForPfs(shippingMethod, isMourning);
    }

    public async Task<decimal> GetInternationalDeliveryFeeForPfs()
    {
        return await _shippingMethodService.GetInternationalDeliveryFeeForPfs();
    }
}
