﻿using commercetools.Base.Client.Error;
using IT.Microservices.CT.Order.Wrapper.Application;
using IT.SharedLibraries.CT.Exceptions;
using Microsoft.AspNetCore.Mvc;

namespace IT.Microservices.CT.Order.Wrapper.Presentation;

public class QueryShippingController(ILogger<QueryShippingController> logger, IShippingUseCase shippingUseCase) : BaseController
{
    [HttpGet("GetDeliveryFeeForPfsByProducts")]
    public async Task<IActionResult> GetDeliveryFeeForPfsByProducts([FromQuery] List<string> productsKey, [FromQuery] List<string> accessoriesKey, [FromQuery] bool isMourning)
    {
        try
        {
            var deliveryCost = await shippingUseCase.GetDeliveryFeeForPfs(productsKey, accessoriesKey, isMourning);
            logger.LogInformation($"Got {deliveryCost.ToString("0.##")} for isMourning {isMourning}");
            return Ok(deliveryCost);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.Message);
        }
        catch (CtCustomException)
        {
            return StatusCode(StatusCodes.Status500InternalServerError);
        }
    }

    [HttpGet("GetDeliveryFeeForPfs")]
    public async Task<IActionResult> GetDeliveryFeeForPfs(bool isMourning)
    {
        try
        {
            var deliveryCost = await shippingUseCase.GetDeliveryFeeForPfs(isMourning);
            logger.LogInformation($"Got {deliveryCost.ToString("0.##")} for isMourning {isMourning}");
            return Ok(deliveryCost);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.Message);
        }
        catch(CtCustomException)
        {
            return StatusCode(StatusCodes.Status500InternalServerError);
        }
    }

    [HttpGet("GetInternationalOutDeliveryFeeForPfs")]
    public async Task<IActionResult> GetInternationalOutDeliveryFeeForPfs()
    {
        try
        {
            var deliveryCost = await shippingUseCase.GetInternationalDeliveryFeeForPfs();
            logger.LogInformation($"Got {deliveryCost.ToString("0.##")}");
            return Ok(deliveryCost);
        }
        catch (BadRequestException e)
        {
            return new BadRequestObjectResult(e.Message);
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.Message);
        }
        catch (CtCustomException)
        {
            return StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
