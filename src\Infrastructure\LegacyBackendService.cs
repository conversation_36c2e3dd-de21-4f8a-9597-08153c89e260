﻿using IT.Microservices.CT.Order.Wrapper.Domain;
using IT.Microservices.CT.Order.Wrapper.Domain.Exceptions;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.HttpClient;
using Microsoft.Extensions.Options;
using System.Net;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;
using SerializerType = ITF.SharedLibraries.ExtensionMethods.Serializer.SerializerType;

namespace IT.Microservices.CT.Order.Wrapper.Infrastructure;

public interface ILegacyBackendService : IHttpClient
{
    Task<OrderCheckRequestResponse> OrderCheck(OrderCheckRequest request);
}
public interface ILegacyBackendSlaveService : IHttpClient
{
    Task<OrderCheckRequestResponse> OrderCheck(OrderCheckRequest request);
}

public class LegacyBackendService(
    System.Net.Http.HttpClient httpClient,
    IConfiguration config,
    ILogger<LegacyBackendService> logger,
    IOptionsMonitor<LegacyBackendEndpointList> legacyBackendEndpointsSettings
        ) : ITF.SharedLibraries.HttpClient.HttpClient(httpClient, config, "LegacyBackendEndpoint", logger), ILegacyBackendService
{
    public async Task<OrderCheckRequestResponse> OrderCheck(OrderCheckRequest request)
    {
        try
        {
            _logger.LogInformation("OrderCheck ({request})", Newtonsoft.Json.JsonConvert.SerializeObject(request));
        }
        catch { }


        OrderCheckRequestResponse result = new OrderCheckRequestResponse();
        try
        {
            var entry = legacyBackendEndpointsSettings.CurrentValue.OrderCheckUpdate;
            System.Net.Http.HttpResponseMessage response = null;
            if (entry != null && entry.Action != null && entry.Endpoint != null)
            {
                switch (entry.Action)
                {
                    case "PUT":
                        response = await PutAsync(request, entry.Endpoint, serializerType: Serializer.SerializerType.NewtonSoft);
                        break;
                    case "POST":
                        response = await PostAsync(request, entry.Endpoint, serializerType: Serializer.SerializerType.NewtonSoft);
                        break;
                    default:
                        _logger.LogWarning("Wrong entry point action, accepted values are [\"PUT\",\"POST\"] - request: {request}", request.Serialize());
                        break;
                }
            }
            else response = await PostAsync(request, "v1/pfs/order/check", serializerType: Serializer.SerializerType.NewtonSoft);

            result = await CheckResponse(request, response);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "{process} error with message {message}", nameof(OrderCheck), e.Message);
            throw;
        }
        return result;
    }
    private async Task<OrderCheckRequestResponse> CheckResponse(OrderCheckRequest request, System.Net.Http.HttpResponseMessage response)
    {


        if (response.StatusCode == HttpStatusCode.BadRequest)
            throw new LegacyBackendExceptions.InvariantException($"Invariant error HTTP BAD REQUEST (floristIdentifier : {request.FloristIdentifier} , orderIdentifier : {request.OrderIdentifier} , HttpStatusCode : {response.StatusCode}): {response.Content?.ReadAsStringAsync()?.Result}");

        if(response.StatusCode == HttpStatusCode.NotFound)
        {
            var json = await response.Content.ReadAsStringAsync();
            return json.Deserialize<OrderCheckRequestResponse>(SerializerType.NewtonSoft);
        }

        if (!response.IsSuccessStatusCode)
            throw new LegacyBackendExceptions.LegacyBackendInfrastructureException($"Sending order to the Italian backend has failed for floristIdentifier {request.FloristIdentifier}, orderIdentifier {request.OrderIdentifier} . HttpStatusCode {response.StatusCode} - Error : {response.Content?.ReadAsStringAsync()?.Result}", response);

        var result = await ReadInformationsAsync<OrderCheckRequestResponse>(response, SerializerType.NewtonSoft);

        _logger.LogInformation("Response json from request ({request}) : {response}", response.RequestMessage.RequestUri.ToString(), result?.Serialize(SerializerType.NewtonSoft));

        return result;
    }


}

public class LegacyBackendSlaveService : ITF.SharedLibraries.HttpClient.HttpClient, ILegacyBackendSlaveService
{
    private readonly IOptionsMonitor<LegacyBackendSlaveEndpointList> _legacyBackendSlaveEndpointsSettings;
    public LegacyBackendSlaveService(
        System.Net.Http.HttpClient httpClient,
        IConfiguration config,
        ILogger<LegacyBackendService> logger,
        IOptionsMonitor<LegacyBackendSlaveEndpointList> legacyBackendSlaveEndpointsSettings
        ) : base(httpClient, config, "LegacyBackendSlaveEndpoint", logger)
    {
        _legacyBackendSlaveEndpointsSettings = legacyBackendSlaveEndpointsSettings;
    }


    public async Task<OrderCheckRequestResponse> OrderCheck(OrderCheckRequest request)
    {
        OrderCheckRequestResponse result = new OrderCheckRequestResponse();
        try
        {
            var entry = _legacyBackendSlaveEndpointsSettings.CurrentValue.OrderCheckUpdate;
            System.Net.Http.HttpResponseMessage response = null;
            if (entry != null && entry.Action != null && entry.Endpoint != null)
            {
                switch (entry.Action)
                {
                    case "PUT":
                        response = await PutAsync(request, entry.Endpoint, serializerType: Serializer.SerializerType.NewtonSoft);
                        break;
                    case "POST":
                        response = await PostAsync(request, entry.Endpoint, serializerType: Serializer.SerializerType.NewtonSoft);
                        break;
                    default:
                        _logger.LogWarning("Wrong entry point action, accepted values are [\"PUT\",\"POST\"] - request: {request}", request.Serialize());
                        break;
                }
            }
            else response = await PostAsync(request, "v1/pfs/order/check", serializerType: Serializer.SerializerType.NewtonSoft);

            result = await CheckResponse(request, response);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "{process} error with message {message}", nameof(OrderCheck), e.Message);
            throw;
        }
        return result;
    }
    private async Task<OrderCheckRequestResponse> CheckResponse(OrderCheckRequest request, System.Net.Http.HttpResponseMessage response)
    {


        if (response.StatusCode == HttpStatusCode.BadRequest)
            throw new LegacyBackendExceptions.InvariantException($"Invariant error HTTP BAD REQUEST (floristIdentifier : {request.FloristIdentifier} , orderIdentifier : {request.OrderIdentifier} , HttpStatusCode : {response.StatusCode}): {response.Content?.ReadAsStringAsync()?.Result}");

        if (response.StatusCode == HttpStatusCode.NotFound)
        {
            var json = await response.Content.ReadAsStringAsync();
            return json.Deserialize<OrderCheckRequestResponse>(SerializerType.NewtonSoft);
        }

        if (!response.IsSuccessStatusCode)
            throw new LegacyBackendExceptions.LegacyBackendInfrastructureException($"Sending order to the Italian backend has failed for floristIdentifier {request.FloristIdentifier}, orderIdentifier {request.OrderIdentifier} . HttpStatusCode {response.StatusCode} - Error : {response.Content?.ReadAsStringAsync()?.Result}", response);

        var result = await ReadInformationsAsync<OrderCheckRequestResponse>(response, SerializerType.NewtonSoft);

        _logger.LogInformation("Response json from request ({request}) : {response}", response.RequestMessage.RequestUri.ToString(), result?.Serialize(SerializerType.NewtonSoft));

        return result;
    }


}
