﻿namespace IT.Microservices.CT.Order.Wrapper.Domain.Exceptions;

public class Exceptions
{
    public class OrderNotFoundException : Exception
    {
        public OrderNotFoundException()
        {
        }
        public OrderNotFoundException(string message) : base(message)
        {
        }

        public OrderNotFoundException(string message, Exception inner) : base(message, inner)
        {
        }
    }

    public class OrderStatusNotConsistentException : Exception
    {
        public OrderStatusNotConsistentException()
        {
        }
        public OrderStatusNotConsistentException(string message) : base(message)
        {
        }

        public OrderStatusNotConsistentException(string message, Exception inner) : base(message, inner)
        {
        }
    }
   
    public class IncorrectOrderActionException : Exception
    {
        public IncorrectOrderActionException(string message) : base(message)
        {
        }
        public IncorrectOrderActionException(string message, Exception inner) : base(message, inner)
        {
        }
    }

    public class InvalidNoteContentLengthException : Exception
    {
        public InvalidNoteContentLengthException(string message) : base(message)
        {
        }
        public InvalidNoteContentLengthException(string message, Exception inner) : base(message, inner)
        {
        }
    }

    public class NoteNotFoundException : Exception
    {
        public NoteNotFoundException(string message) : base(message)
        {
        }
        public NoteNotFoundException(string message, Exception inner) : base(message, inner)
        {
        }
    }
}
