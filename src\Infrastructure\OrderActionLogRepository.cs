﻿using IT.Microservices.CT.Order.Wrapper.Domain;
using ITF.SharedLibraries.MongoDB.Repository;
using MongoDB.Driver;

namespace IT.Microservices.CT.Order.Wrapper.Infrastructure;

public interface IOrderActionLogRepository : IMongoRepository<OrderActionLogModel>
{
}

public class OrderActionLogRepository(ITF.SharedLibraries.MongoDB.Configuration configuration, IMongoClient mongoClient) : MongoRepository<OrderActionLogModel>(mongoClient, configuration.DatabaseName, "orderActionLog"), IOrderActionLogRepository
{
}
