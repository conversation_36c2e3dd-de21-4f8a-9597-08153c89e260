﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderCreatedMessage : BaseMessage<LegacyOrderCreatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderNumber ?? Payload?.LegacyOrderNumber;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public LegacyOrderCreatedPayload ConvertPayload(BaseOrderPayload payload)
                {

                    var legacyPayload = new LegacyOrderCreatedPayload
                    {
                        EventDate = payload.EventDate,
                        EventID = payload.EventID,
                        ExecutingFloristIdentifier = payload.FloristId ?? string.Empty,
                        ExecutingFloristType = string.Empty,
                        DeliveryContryCode = payload.Recipient?.CountryCode ?? string.Empty,
                        SenderCountryCode = string.IsNullOrEmpty(payload.InternationalOrderId) ? "FR" : payload.TransmitterFloristId?.Substring(0, 2) ?? string.Empty,
                        Src = payload.Source ?? string.Empty,
                        SenderFloristIdentifier = payload.TransmitterFloristId ?? string.Empty,
                        OrderNumber = payload.OrderId,
                        CtOrderId = string.Empty,
                        LegacyOrderNumber = payload.OrderId,
                        OrderTotal = payload.TotalAmount.HasValue ? Convert.ToDecimal(payload.TotalAmount.Value) : (decimal?)null,
                        CurrencyCode = "EUR",
                        CardMessage = payload.Message ?? string.Empty,
                        CreatedAt = payload.OrderDate,
                        Notes = payload.Delivery?.AdditionalAddress ?? string.Empty,
                        Signature = payload.Signature ?? string.Empty,
                        Status = payload.Status?.GetCtStatus() ?? string.Empty,
                        ContactTitle = (payload.Recipient?.Greetings?.Equals("MME", StringComparison.OrdinalIgnoreCase) ?? false) ? "MRS" : payload.Recipient?.Greetings ?? string.Empty,
                        Billing = new LegacyBilling
                        {
                            FirstName = payload.Customer?.FirstName ?? string.Empty,
                            LastName = payload.Customer?.LastName ?? string.Empty,
                            Email = payload.Customer?.Email ?? string.Empty,
                            Address = payload.Billing?.Street ?? string.Empty,
                            City = payload.Billing?.City ?? string.Empty,
                            ZipCode = payload.Billing?.ZipCode ?? string.Empty,
                            CountryCode = string.IsNullOrEmpty(payload.InternationalOrderId) ? "FR" : payload.TransmitterFloristId?.Substring(0, 2) ?? string.Empty,
                            Mobile = payload.Customer?.Phone ?? string.Empty,
                            FiscalCode = string.Empty,
                            CompanyName = payload.Customer?.CompanyName ?? string.Empty,
                            CareOf = string.Empty
                        },
                        Shipping = new LegacyShipping
                        {
                            FirstName = payload.Recipient?.FirstName ?? string.Empty,
                            LastName = payload.Recipient?.LastName ?? string.Empty,
                            Email = string.Empty,
                            StreetName = payload.Recipient?.Street ?? string.Empty,
                            StreetNumber = string.Empty,
                            City = payload.Recipient?.City ?? string.Empty,
                            ZipCode = payload.Recipient?.ZipCode ?? string.Empty,
                            CountryCode = payload.Recipient?.CountryCode ?? string.Empty,
                            Province = string.Empty,
                            Mobile = payload.Recipient?.MainPhone ?? string.Empty,
                            DeliveryDate = payload.Delivery?.Date ?? DateTime.MinValue,
                            Moment = payload.Delivery!.WindowRaoToMomentCT(),          
                            Time = TimeSpan.TryParse(payload.Delivery?.Time ?? "", out var timeReceived) ? timeReceived.ToString(@"hh\:mm") : "",
                            Longitude = payload.Recipient?.Longitude ?? 0,
                            Latitude = payload.Recipient?.Latitude ?? 0,
                            CompanyName = payload.Delivery?.Place ?? string.Empty,
                            Comments = payload.Delivery?.Instructions ?? string.Empty,

                        },
                        Products = payload.Products?.GetEligibleProducts().Select(product => (LegacyProduct)product).ToList() ?? new()
                    };
                    //if (string.IsNullOrEmpty(legacyPayload?.Shipping?.Time) && !string.IsNullOrEmpty(payload?.Delivery?.Time ?? null)) // it will be remove by GetDifferencesForUpdate !
                    //{
                    //    if (string.IsNullOrEmpty(legacyPayload.Shipping.Comments))
                    //        legacyPayload.Shipping.Comments = payload?.Delivery?.Time ?? string.Empty;
                    //    else
                    //        legacyPayload.Shipping.Comments = string.Join(' ', legacyPayload.Shipping.Comments, payload?.Delivery?.Time ?? string.Empty);
                    //}


                    return legacyPayload ?? new();

                }
                
            }
        }
    }

    public class LegacyOrderCreatedPayload : LegacyPayload, IEquatable<LegacyOrderCreatedPayload>
    {
        public string DeliveryContryCode { get; set; }
        public string SenderCountryCode { get; set; }
        public string Src { get; set; }
        //public string DeliveryFloristIdentifier { get; set; }
        public string SenderFloristIdentifier { get; set; }
        //public decimal? DeliveryFloristDeliveryAmount { get; set; } = null;
        public string OrderNumber { get; set; }
        public string CtOrderId { get; set; }
        public decimal? OrderTotal { get; set; } = null;
        public string CurrencyCode { get; set; }
        public string CardMessage { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public string LegacyOrderNumber { get; set; }
        public string ExecutingFloristIdentifier { get; set; }
        public string ExecutingFloristType { get; set; }
        public string Signature { get; set; } = string.Empty;
        public string ContactTitle { get; set; } = string.Empty;
        public LegacyBilling Billing { get; set; } = new();
        public LegacyShipping Shipping { get; set; } = new();
        public List<LegacyProduct> Products { get; set; } = new();

      
        

        public bool Equals(LegacyOrderCreatedPayload parameter)
        {
            if(parameter == null)
                return false;

            return (DeliveryContryCode == parameter.DeliveryContryCode &&
                SenderCountryCode == parameter.SenderCountryCode &&
                Src == parameter.Src &&
                //DeliveryFloristIdentifier == parameter.DeliveryFloristIdentifier &&
                SenderFloristIdentifier == parameter.SenderFloristIdentifier &&
                //DeliveryFloristDeliveryAmount == parameter.DeliveryFloristDeliveryAmount &&
                OrderNumber == parameter.OrderNumber &&
                CtOrderId == parameter.CtOrderId &&
                OrderTotal == parameter.OrderTotal &&
                CurrencyCode == parameter.CurrencyCode &&
                CardMessage == parameter.CardMessage &&
                CreatedAt == parameter.CreatedAt &&
                Notes == parameter.Notes &&
                Status == parameter.Status &&
                LegacyOrderNumber == parameter.LegacyOrderNumber &&
                ExecutingFloristIdentifier == parameter.ExecutingFloristIdentifier &&
                ExecutingFloristType == parameter.ExecutingFloristType &&
                Signature == parameter.Signature &&
                ContactTitle == parameter.ContactTitle &&
                Billing.Equals(parameter.Billing) &&
                Shipping.Equals(parameter.Shipping) &&
                Products.Equals(parameter.Products)
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderCreatedPayload);
        }

        public override int GetHashCode() => new
        {
            DeliveryContryCode,
            Src,
            //DeliveryFloristIdentifier,
            SenderFloristIdentifier,
            //DeliveryFloristDeliveryAmount,
            OrderNumber,
            CtOrderId,
            OrderTotal,
            CurrencyCode,
            CardMessage,
            CreatedAt,
            Notes,
            LegacyOrderNumber,
            Status,
            Signature,
            ContactTitle,
            Billing,
            Shipping,
            Products
        }.GetHashCode();
    }

    public class LegacyBilling
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Mobile { get; set; }
        public string FiscalCode { get; set; }
        public string CompanyName { get; set; }
        public string CareOf { get; set; } // address 2
        public bool Equals(LegacyBilling parameter)
        {
            return (FirstName == parameter.FirstName &&
                LastName == parameter.LastName &&
                Email == parameter.Email &&
                Address == parameter.Address &&
                City == parameter.City &&
                ZipCode == parameter.ZipCode &&
                CountryCode == parameter.CountryCode &&
                Mobile == parameter.Mobile &&
                FiscalCode == parameter.FiscalCode &&
                CompanyName == parameter.CompanyName &&
                CareOf == parameter.CareOf
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyBilling);
        }


        public override int GetHashCode() => new
        {
            FirstName,
            LastName,
            Email,
            Address,
            City,
            ZipCode,
            CountryCode,
            Mobile,
            FiscalCode,
            CompanyName,
            CareOf,
        }.GetHashCode();
    }

    public class LegacyShipping
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string StreetName { get; set; }
        public string StreetNumber { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Province { get; set; }
        public string Mobile { get; set; }
        public DateTime DeliveryDate { get; set; }
        public MomentEnum Moment { get; set; }
        public string Time { get; set; } // HH:mm
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string Comments { get; set; }
        public bool Equals(LegacyShipping parameter)
        {
            return (FirstName == parameter.FirstName &&
                LastName == parameter.LastName &&
                Email == parameter.Email &&
                StreetName == parameter.StreetName &&
                StreetNumber == parameter.StreetNumber &&
                City == parameter.City &&
                ZipCode == parameter.ZipCode &&
                CountryCode == parameter.CountryCode &&
                Province == parameter.Province &&
                Mobile == parameter.Mobile &&
                DeliveryDate == parameter.DeliveryDate &&
                Moment == parameter.Moment &&
                Time == parameter.Time &&
                Longitude == parameter.Longitude &&
                Latitude == parameter.Latitude &&
                CompanyName == parameter.CompanyName &&
                Comments == parameter.Comments
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyShipping);
        }


        public override int GetHashCode() => new
        {
            FirstName,
            LastName,
            Email,
            StreetName,
            StreetNumber,
            City,
            ZipCode,
            CountryCode,
            Province,
            Mobile,
            DeliveryDate,
            Moment,
            Time,
            Longitude,
            Latitude,
            CompanyName,
            Comments
        }.GetHashCode();
    }

    public class LegacyProduct
    {
        public string Name { get; set; }
        public string ProductKey { get; set; }
        public string VariantKey { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public string Description { get; set; }
        public string IsAccessoryFor { get; set; }
        public string RibbonText { get; set; }
        public decimal? MarketingFee { get; set; } = null;
        public bool IsExternalPrice { get; set; }
        public string Location { get; set; }
        public decimal? ExecutingFloristAmount { get; set; }

        public static implicit operator LegacyProduct(ProductInformations product)
        {
            var legacyProduct = new LegacyProduct
            {
                RibbonText = product.RibbonText ?? string.Empty,
                IsAccessoryFor = string.Empty,
                Price = Convert.ToDecimal(product.Price),
                ProductKey = product.GetProductKey(),
                VariantKey = product.GetVariantKey(),
                Quantity = product.Quantity,
                Description = product.Description ?? string.Empty,
                Name = product.Label ?? product.ProductId,
                MarketingFee = Convert.ToDecimal(product.Margin),
                IsExternalPrice = product.Size?.Equals("PL", StringComparison.OrdinalIgnoreCase) ?? false,
                Location = product.Style?.ToUpper() ?? string.Empty,
                ExecutingFloristAmount = product.GetExecutingFloristAmount(),

            };

            return legacyProduct;
        }
        public bool Equals(LegacyProduct parameter)
        {
            return (Name == parameter.Name &&
                ProductKey == parameter.ProductKey &&
                VariantKey == parameter.VariantKey &&
                Quantity == parameter.Quantity &&
                Price == parameter.Price &&
                Description == parameter.Description &&
                IsAccessoryFor == parameter.IsAccessoryFor &&
                RibbonText == parameter.RibbonText &&
                MarketingFee == parameter.MarketingFee &&
                IsExternalPrice == parameter.IsExternalPrice &&
                Location == parameter.Location &&
                ExecutingFloristAmount == parameter.ExecutingFloristAmount

                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyProduct);
        }


        public override int GetHashCode() => new
        {
            Name,
            ProductKey,
            VariantKey,
            Quantity,
            Price,
            Description,
            IsAccessoryFor,
            RibbonText,
            MarketingFee,
            IsExternalPrice,
            Location,
            ExecutingFloristAmount
        }.GetHashCode();

    }
}
