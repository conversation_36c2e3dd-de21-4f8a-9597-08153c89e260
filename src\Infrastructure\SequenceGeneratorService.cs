﻿using HttpClient = ITF.SharedLibraries.HttpClient.HttpClient;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using ITF.SharedLibraries.EnvironmentVariable;

namespace IT.Microservices.CT.Order.Wrapper.Infrastructure;

public interface ISequenceGeneratorService
{
    Task<long> GetNext();
}

public class SequenceGeneratorService : HttpClient, ISequenceGeneratorService
{
    private readonly IConfiguration _config;
    public SequenceGeneratorService(System.Net.Http.HttpClient httpClient, IConfiguration config, ILogger<SequenceGeneratorService> logger) : base(httpClient, config, "SequenceGeneratorGetNextEndpoint", logger)
    {
        _config = config;
    }

    public async Task<long> GetNext()
    {
        var httpResponseMessages = await GetAsync("/Sequence/GenerateNextSequence");

        httpResponseMessages.EnsureSuccessStatusCode();
        var json = await httpResponseMessages.Content.ReadAsStringAsync();
        var orderWrapperSetting = _config.Get<CTOrderWrapperSettings>("CTOrderWrapperSettings");
        
        if(orderWrapperSetting.SequenceNumberNonNumeric && !string.IsNullOrEmpty(json))
        {
            json = json.Substring(2);
        }
        if (long.TryParse(json, out var sequenceId)) { return sequenceId; }
        return -1;
    }
}
