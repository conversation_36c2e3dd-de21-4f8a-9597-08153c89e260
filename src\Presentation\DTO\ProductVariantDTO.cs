﻿namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class ProductVariantDTO
{
    // the variant key
    public string VariantKey { get; set; }

    // the key of the parent product
    public string ProductKey { get; set; }

    // size key
    public string SizeKey { get; set; }

    public decimal Price { get; set; }
    public List<PriceTiers> PriceTiers { get; set; } = new List<PriceTiers>();
    public string Color { get; set; }
    public int MinimumQuantity { get; set; }
    public int MaximumQuantity { get; set; }
    public bool IsSingleFlowerBouquet { get; set; }
    //public string Description { get; set; }
    public string Image { get; set; }

    public ProductVariantDTO(string variantKey, string productKey, string sizeKey, string imageUrl, string color, int minimumQuantity, int maximumQuantity)
    {
        VariantKey = variantKey;
        ProductKey = productKey;
        SizeKey = sizeKey;
        //Description = description;
        Image = imageUrl;
        Color = color;
        MinimumQuantity = minimumQuantity;
        MaximumQuantity = maximumQuantity;
        IsSingleFlowerBouquet = (minimumQuantity > 0) && (minimumQuantity != maximumQuantity);
    }
}
public class PriceTiers
{
    public long MinimumQuantity { get; set; }
    public string CurrencyCode { get; set; }
    public decimal Price { get; set; }
}
