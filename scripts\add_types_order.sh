#!/bin/sh

CTP_CLIENT_ID=Cc8myss63s7MHTVTu7HJhZcj
CTP_CLIENT_SECRET=jtmrGQdMjPQck2vQ20ni_50lISVG6VRA
CTP_PROJECT_KEY=myflower-dev

#CTP_CLIENT_ID=v3TQObln_UYSQeoDUy4O-JLE
#CTP_CLIENT_SECRET=70jmEpRljUpt9_4bfm_EK2XLxShF1Oyr
#CTP_PROJECT_KEY=interfloratest

set -e
echo "CTP_CLIENT_ID= ${CTP_CLIENT_ID}"
echo "CTP_CLIENT_SECRET= ${CTP_CLIENT_SECRET}"
echo "CTP_PROJECT_KEY= ${CTP_PROJECT_KEY}"
echo

CTP_API_URL="https://api.europe-west1.gcp.commercetools.com"
CTP_AUTH_URL="https://auth.europe-west1.gcp.commercetools.com"
CTP_SCOPES="manage_project:${CTP_PROJECT_KEY}"
base="${CTP_API_URL}/${CTP_PROJECT_KEY}"

token="$(curl -s -u "${CTP_CLIENT_ID}:${CTP_CLIENT_SECRET}" \
  -d "grant_type=client_credentials&scope=manage_project:${CTP_PROJECT_KEY}" \
  "https://auth.europe-west1.gcp.commercetools.com/oauth/token" \
  | jq -r '.access_token' \
)"

echo
echo "Token = ${token}"

_curl() {
  curl -s -H "Authorization: Bearer ${token}" $@
}

set -x

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')

echo
echo "current_version = ${current_version}"

more_fields=$(jq -rc << EOF
{"actions": [
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "String"},"name": "floristOrderStatus","label": {"en": "florist order status"},"required": false}},
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "String"},"name": "transmitterFloristId","label": {"en": "transmitter florist id"},"required": false}},
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "String"},"name": "executingFloristId","label": {"en": "executing florist id"},"required": false}},
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "Boolean"}, "name": "readByExecutingFlorist", "label": {"en": "order has been read by executing florist"}, "required": false}}, 
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "Boolean"}, "name": "deliveryInProgress", "label": {"en": "delivery in progress"},"required": false}},
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "String"}, "name": "legacyOrderNumber", "label": {"en": "legacy order number"},"required": false}},
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "Money"}, "name": "executingFloristDeliveryAmount", "label": {"en": "executing florist delivery amount"},"required": false}},
	{"action": "addFieldDefinition","fieldDefinition": {"type": {"name": "String"}, "name": "internalOrderId", "label": {"en": "internal order id (florist order number)"},"required": false}}
] ,"version": ${current_version}}
EOF
)

echo
echo "• adding fields"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-
