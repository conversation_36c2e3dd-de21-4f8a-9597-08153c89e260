meta {
  name: Add cart types toBeAcceptedBefore
  type: http
  seq: 9
}

post {
  url: https://api.europe-west1.gcp.commercetools.com/{{CTP_PROJECT_KEY}}/types/key=cart-custom
  body: json
  auth: bearer
}

auth:bearer {
  token: {{TOKEN}}
}

body:json {
  {
    "actions": [
      {
        "action": "addFieldDefinition",
        "fieldDefinition": {"type": {"name": "DateTime"},"name": "toBeAcceptedBefore","label": {"en": "datetime limit for the order to be accepted by the executor florist"},"required": false}
      }
    ],
    "version": 18
  }
}
