﻿using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Order;
using MongoDB.Driver;

namespace IT.Microservices.CT.Order.Wrapper.Infrastructure;

public interface IOrderNoteRepository : IMongoRepository<GlobalOrderNoteModel>
{
    Task<List<GlobalOrderNoteModel>> GetNoteByOrderId(string orderId);
}

public class OrderNoteRepository(IMongoClient mongoClient) : MongoRepository<GlobalOrderNoteModel>(mongoClient, "order_notes", "order-note-subset"), IOrderNoteRepository
{
    public async Task<List<GlobalOrderNoteModel>> GetNoteByOrderId(string orderId)
    {
        var notes = await FilterByAsync(n => n.OrderId == orderId);
        
        return notes?.OrderByDescending(x => x.CreatedAt).ToList();
    }
}
