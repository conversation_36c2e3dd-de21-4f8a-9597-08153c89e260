﻿using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;

namespace IT.Microservices.CT.Order.Wrapper.Application;

public interface IOrderQueryFacade
{
    Task<OrderPagedQueryResponse> GetToExecuteOrdersSorted(string floristId, bool sortByDeliveryDate, int limit);
}
public class OrderQueryFacade : IOrderQueryFacade
{
    private readonly IOrderQueryService _orderQueryService;

    public OrderQueryFacade(IOrderQueryService orderQueryService)
    {
        _orderQueryService = orderQueryService;
    }

    public async Task<OrderPagedQueryResponse> GetToExecuteOrdersSorted(string floristId, bool sortByDeliveryDate, int limit)
    {
        var response = await _orderQueryService.GetToExecuteOrders(floristId, sortByDeliveryDate, limit);

        List<IOrder> orders = (List<IOrder>)response.Results;
        orders.Sort((a, b) =>
        {
            if ((OrderExtensionMethods.GetInternalOrderId(a) == null) && (OrderExtensionMethods.GetInternalOrderId(b) != null)) return -1;
            if ((OrderExtensionMethods.GetInternalOrderId(a) != null) && (OrderExtensionMethods.GetInternalOrderId(b) == null)) return 1;
            if ((OrderExtensionMethods.GetInternalOrderId(a) == null) && (OrderExtensionMethods.GetInternalOrderId(b) == null)) return 0;

            var arrA = OrderExtensionMethods.GetInternalOrderId(a).Split('-');
            var arrB = OrderExtensionMethods.GetInternalOrderId(b).Split('-');
            var keyA1 = Int32.Parse(arrA[0]);
            var keyA2 = Int32.Parse(arrA[1]);
            var keyB1 = Int32.Parse(arrB[0]);
            var keyB2 = Int32.Parse(arrB[1]);

            // Compare the 2 keys
            if (keyA1 > keyB1) return 1;
            if (keyA1 < keyB1) return -1;
            if (keyA2 > keyB2) return 1;
            if (keyA2 < keyB2) return -1;
            return 0;
        });
        response.Results = orders;
        return response;
    }
}
