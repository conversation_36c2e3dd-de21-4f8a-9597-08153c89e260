﻿using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;

namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class OrdersCountDTOBuilder
{

    private OrdersCountDTO _orderCountDTO = new();

    public OrdersCountDTOBuilder()
    {
        _orderCountDTO = new();
    }

    public void WithCTOrders(IList<IOrder> src)
    {
        var todayMin = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
        var todayMax = todayMin.AddHours(23).AddMinutes(59).AddSeconds(59);
        var tomorrowMax = todayMax.AddDays(1);
        
        foreach (var order in src)
        {
            string deliveryDate = OrderExtensionMethods.GetCustomString(CtOrderCustomAttributesNames.ShippingAddress.DATE, order.ShippingAddress?.Custom?.Fields ?? null);
            if (DateTime.TryParse(deliveryDate, out var deliveryDateParsed))
            {
                if(DateTime.Compare(deliveryDateParsed, todayMin) < 0)
                {
                    _orderCountDTO.Late++;
                }
                else if(DateTime.Compare(deliveryDateParsed, todayMax) < 0)
                {
                    _orderCountDTO.Today++;
                }
                else if(DateTime.Compare(deliveryDateParsed, tomorrowMax) < 0)
                {
                    _orderCountDTO.Tomorrow++;
                }
                else
                {
                    _orderCountDTO.Later++;
                }
            }
        }
    }

    public OrdersCountDTO Build()
    {
        OrdersCountDTO result = _orderCountDTO;
        _orderCountDTO = new OrdersCountDTO();
        return result;
    }
}
