###
# Values for preprod environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/IT.Microservices.CT.Order.Wrapper/"
#helm diff upgrade itctorderwrapper ${helmChartPath} --values chart-values/values-preprod-france.yaml -n itf-ms --set 'image.tag=latest,image.repository=itfpreprodacr.azurecr.io/itctorderwrapper'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "itctorderwrapper"
fullnameOverride: "itctorderwrapper"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "itf-ms-itctorderwrapper"

dotnetProgramName: "IT.Microservices.CT.Order.Wrapper.dll"

podAnnotations:
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/itctorderwrapper.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"  
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "itf-microservices"
  # vault.hashicorp.com/agent-inject-secret-itctorderwrapper.pass: "applications/itf-microservices"

  # Inject secret via a configmap named itctorderwrapper-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'itctorderwrapper-secrets'
  
  #inject secret via env variables 
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  vault.hashicorp.com/role: 'itf-microservices'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/itf-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/itf-microservices" -}}
      export UNLEASH__KEY={{ .Data.itf_unleash_key }}
      export Client__ClientId={{ .Data.itf_microservice_write_id }}
      export Client__ClientSecret={{ .Data.itf_microservice_write_password }}
      export SlackAlert__ApiToken={{ .Data.slack_webhook }}
    {{- end }}

service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.preprod.interflora.fr
  path: "/itctorderwrapper"
  tls:
  - hosts:
    - microservices.preprod.interflora.fr
    secretName: "preprod-interflora-fr-cert"  
  enabled: true
  ingressClass: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "http://localhost:3000,https://*.interflora.fr,https://*.preprod.interflora.fr"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow *************/32;
      allow *************/32;
      allow ***************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/itctorderwrapper$ /itctorderwrapper/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

livenessProbe:
  httpGet:
    path: /health
    port: 80
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 100

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 

  CTOrderWrapperSettings: |-
    "CTOrderWrapperSettings": {
      "CountryCode" : "FR",
      "Language": "fr",
      "PFsGetOrderDocumentUrlFormat": "http://itmsdocuments.itf-ms/itmsdocuments/api/v1/GetOrderDoc?orderId={orderIdentifier}&floristId={floristIdentifier}&type={type}",
      "SequenceNumberNonNumeric": true,
       "FrenchSettings": {
        "OrderNumberPrefix": "IF",
        "ResultsReturnedByAvailability": 0,
        "MorningLimitHour": 11,
        "MorningLimitMinutes": 0,
        "AfternoonLimitHour": 17,
        "AfternoonLimitMinutes": 30,
        "ProductMinPrice": 16,
        "ProductMaxPrice": 20000
      }
    }

  CommercetoolClient: |-
    "Client": {
      "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
      "ProjectKey": "myflower-preprod",
      "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/"
    }

  sequenceGeneratorEndpointConfig: |-
    "SequenceGeneratorGetNextEndpoint": {
      "Url": "http://itsequencegenerator.itf-ms/itsequencegenerator/api/v1",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  CommerceToolCustomSettings: |-
    "CommerceToolCustomSettings": {
      "LocalCountryCode": "FR",
      "LocalCountryChannelKey": "interflora.fr",
      "LocalCountryStoreKey": "ITF",
      "CtMoruningProductTypeKey": "mourning",
      "LocalCountryAccessoriesCategoryKey": "ACC",
      "LocalCountryProductsCategoryKey": "category",
      "OutboundOrderShippingMethodKey": "pfs-international-france",
      "MourningShippingMethodKey": "mourning",
      "PfsShippingMethodKey": "pfs"
    }

  SlackAlertConfig: |-
    "SlackAlert": {
      "DefaultChannel": "alerts-ms-fr-preprod",
      "BotName": "Error Alert Bot",
      "MaxRetryAttempts": 3
    }

  Kafka: |-
    "Kafka": {
      "TopicsToCreateConfigurations": [
        {
          "TopicName": "order",
          "ReplicationFactor": 1,
          "NumberOfPartitions": 1,
          "RetentionMs": 7200000 //2h
        }
      ]
    }

  KafkaTopicsSettings: |-
    "KafkaTopicsSettings": {
      "Order": "order"
    }

  OrderSettings: |-
    "OrderSettings": {
      "Enabled": false  
    }

  OrderConfirmationSettings: |-
    "OrderConfirmationSettings": {
      "Base64Key_256bits": "*******************************************=",
      "Base64Iv_128bits": "sA9mD2QeD7h/NFwEaRK/wA=="
    }
    
  RAOEndpoint: |-
    "RAOEndpoint": {
      "Url": "https://itf-tst-rao-aap.azurewebsites.net",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  
  AvailabilityEndpoint: |-
    "AvailabilityEndpoint": {
      "Url": "http://availability.itf-ms/availability/api/v1/Availability",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  serilogConfig: |-       
    "Serilog": {
      "Using": [],
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "Microsoft": "Warning",
          "System": "Information",
          "Elastic": "Warning",
          "Apm": "Warning"
        }
      },
      "WriteTo": [
        {
          "Name": "Console"
        }
      ],
      "Enrich": [
        "FromLogContext",
        "WithMachineName",
        "WithProcessId",
        "WithThreadId"
      ],
      "Properties": {
        "ApplicationName": "itf-itctorderwrapper"
      }
    }

env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "preprod"

  - name: ASPNETCORE_COUNTRY
    value: "fr"

  - name: MongoDb__DatabaseName
    value: "it-florist"
    
  - name: MongoDb__ConnectionString
    value: "mongodb://itf-ms-mongodb-0.itf-ms-mongodb-headless.itf-ms-common.svc.cluster.local:27017,itf-ms-mongodb-1.itf-ms-mongodb-headless.itf-ms-common.svc.cluster.local:27017,itf-ms-mongodb-2.itf-ms-mongodb-headless.itf-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: ElasticApm__Environment
    value: "itf-preprod"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "itf-itctorderwrapper"

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "itf-ms-kafka-headless.itf-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "itf-ms-redis-headless.itf-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://itf-unleash.itf-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "itf-itctorderwrapper"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "production"