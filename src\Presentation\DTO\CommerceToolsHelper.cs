﻿using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ProductTypes;
using commercetools.Sdk.Api.Models.Types;
using IT.SharedLibraries.CT.CustomAttributes;

namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;
public static class CommerceToolsHelper
{
    public static bool GetCustomBoolean(string key, IFieldContainer fields)
    {
        if (fields == null)
        {
            return false;
        }
        if (fields.ContainsKey(key))
        {
            string field = fields[key].ToString();

            if (bool.TryParse(field, out bool booleanValue))
            {
                return booleanValue;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    public static bool GetBooleanAttribute(string key, IList<IAttribute> attributes)
    {
        if (attributes == null)
        {
            return false;
        }

        IAttribute a = attributes.Where(a => a.Name.Equals(key)).FirstOrDefault();
        if (a != null && a.IsBooleanAttribute())
        {
            return a.ToBooleanAttribute().GetValue();
        }
        return false;
    }

    public static decimal GetDecimalAttribute(string key, IList<IAttribute> attributes)
    {
        if (attributes == null)
        {
            return 0;
        }

        IAttribute a = attributes.Where(a => a.Name.Equals(key)).FirstOrDefault();
        if (a != null && a.IsMoneyAttribute())
        {
            var money = a.ToMoneyAttribute().GetValue();
            return money.CentAmount / (decimal)Math.Pow(10, money.FractionDigits);
        }
        return 0;
    }

    public static long GetLongAttribute(string key, IList<IAttribute> attributes)
    {
        if (attributes == null)
        {
            return 0;
        }

        IAttribute a = attributes.Where(a => a.Name.Equals(key)).FirstOrDefault();
        if (a != null && a.IsLongAttribute())
        {
            return a.ToLongAttribute().GetValue();
        }
        return 0;
    }

    public static string GetSize(IList<IAttribute> attributes, string localCode)
    {
        if (attributes == null)
        {
            return string.Empty;
        }

        foreach(IAttribute a in attributes)
        { 
            if(a.GetType() == typeof(LocalizedEnumAttribute) && a.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE)
            {
                var size = a as LocalizedEnumAttribute;
                return GetLocalizedStringDTO(size, localCode);
            }
        }

        return string.Empty;
    }

    public static string GetColor(IList<IAttribute> attributes)
    {          
        return ((AttributeLocalizedEnumValue)attributes?.FirstOrDefault(a => a.GetType() == typeof(LocalizedEnumAttribute) && a.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR)?.Value)?.Key ?? "";
          
    }
    public static int GetQuantity(IList<IAttribute> attributes, string quantityAttribute)
    {                     
        return Convert.ToInt32(attributes?.FirstOrDefault(a => a.GetType() == typeof(LongAttribute) && a.Name == quantityAttribute)?.Value ?? 0);                                 
    }

    public static string GetDescription(IList<IAttribute> attributes, string localCode)
    {

        if (attributes == null)
        {
            return string.Empty;
        }

        foreach (IAttribute a in attributes)
        {
            if (a.GetType() == typeof(LocalizedString) && a.Name == CtProductCustomAttributesNames.VariantAttributes.SUMMARY)
            {
                var summary = a as LocalizedString;
                foreach(KeyValuePair<string, string> l in summary)
                {
                    if(string.Equals(l.Key, localCode, StringComparison.OrdinalIgnoreCase))
                    {
                        return l.Value;
                    }
                }
            }
        }

        return string.Empty;
    }

    public static string GetLocalizedString(ILocalizedString locString, string localCode)
    {
        if (locString == null)
        {
            return string.Empty;
        }

        foreach (KeyValuePair<string, string> l in locString)
        {
            if (string.Equals(l.Key, localCode, StringComparison.OrdinalIgnoreCase))
            {
                return l.Value;
            }
        }

        return string.Empty;
    }

    private static string GetLocalizedStringDTO(LocalizedEnumAttribute attribute, string localCode)
    {
        if (attribute == null)
        {
            return string.Empty;
        }
        
        var value = attribute.Value;
        if (value.GetType() == typeof(AttributeLocalizedEnumValue))
        {
            var enumValue = (AttributeLocalizedEnumValue) value;
            foreach (KeyValuePair<string, string> l in enumValue.Label)
            {
                if (string.Equals(l.Key, localCode, StringComparison.OrdinalIgnoreCase))
                {
                    return l.Value;
                }
            }
        }

        return string.Empty;
    }
}
