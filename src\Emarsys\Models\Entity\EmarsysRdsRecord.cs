using Newtonsoft.Json;

namespace ITF.SharedLibraries.Emarsys.Models.Entity
{
    public class EmarsysRdsRecord
    {
        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;

        [JsonProperty("email")]
        public string Email { get; set; } = string.Empty;

        [JsonProperty("response_date")]
        public string ResponseDate { get; set; } = string.Empty;

        [JsonProperty("name_relative")]
        public string NameRelative { get; set; } = string.Empty;

        [JsonProperty("birthdate_relative")]
        public string BirthdateRelative { get; set; } = string.Empty;

        [JsonProperty("month_bd")]
        public int MonthBd { get; set; }

        [JsonProperty("day_bd")]
        public int DayBd { get; set; }

        [JsonProperty("business_unit")]
        public string BusinessUnit { get; set; } = string.Empty;

        [JsonProperty("form_type")]
        public string FormType { get; set; } = string.Empty;

        [JsonProperty("origin")]
        public string Origin { get; set; } = string.Empty;
    }
}