###
# Values for preprod environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/IT.Microservices.CT.Order.Wrapper/"
#helm diff upgrade itctorderwrapper ${helmChartPath} --values chart-values/values-preprod-france.yaml -n itp-ms --set 'image.tag=latest,image.repository=itppreprodacr.azurecr.io/itctorderwrapper'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "itctorderwrapper"
fullnameOverride: "itctorderwrapper"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "itp-ms-itctorderwrapper"

dotnetProgramName: "IT.Microservices.CT.Order.Wrapper.dll"

podAnnotations:
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/itctorderwrapper.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"  
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "itp-microservices"
  # vault.hashicorp.com/agent-inject-secret-itctorderwrapper.pass: "applications/itp-microservices"

  # Inject secret via a configmap named itctorderwrapper-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'itctorderwrapper-secrets'
  
  #inject secret via env variables 
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  vault.hashicorp.com/role: 'itp-microservices'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/itp-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/itp-microservices" -}}
      export LegacyBackendEndpoint__Authentication__Credentials__client_id={{ .Data.itp_backend_api_user }}
      export LegacyBackendEndpoint__Authentication__Credentials__client_secret={{ .Data.itp_backend_api_password }}
      export Client__ClientId={{ .Data.itp_microservice_write_id }}
      export Client__ClientSecret={{ .Data.itp_microservice_write_password }}
      export SlackAlert__ApiToken={{ .Data.slack_webhook }}
    {{- end }}


service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.preprod.interflora.pt
  path: "/itctorderwrapper"
  tls:
  - hosts:
    - microservices.preprod.interflora.pt
    secretName: "preprod-interflora-pt-cert"  
  enabled: true
  ingressClass: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "http://localhost:3000,https://*.interflora.pt,https://*.preprod.interflora.pt"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow *************/32;
      allow *************/32;
      allow ***************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/itctorderwrapper$ /itctorderwrapper/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

livenessProbe:
  httpGet:
    path: /health
    port: 80
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 100

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 

  CTOrderWrapperSettings: |-
    "CTOrderWrapperSettings": {
      "CountryCode" : "PT",
      "Language": "pt",
      "PFsGetOrderDocumentUrlFormat": "http://itmsdocuments.itp-ms/itmsdocuments/api/v1/GetOrderDoc?orderId={orderIdentifier}&floristId={floristIdentifier}&type={type}"
    }

  CommercetoolClient: |-
    "Client": {
      "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
      "ProjectKey": "myflower-preprod",
      "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/"
    }

  sequenceGeneratorEndpointConfig: |-
    "SequenceGeneratorGetNextEndpoint": {
      "Url": "http://itsequencegenerator.itp-ms/itsequencegenerator/api/v1",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  CommerceToolCustomSettings: |-
    "CommerceToolCustomSettings": {
      "LocalCountryCode": "PT",
      "LocalCountryChannelKey": "interflora.pt",
      "LocalCountryStoreKey": "ITP",
      "CtMoruningProductTypeKey": "mourning",
      "LocalCountryAccessoriesCategoryKey": "ite-itp-general-accessories",
      "LocalCountryProductsCategoryKey": "ite-itp-products",
      "OutboundOrderShippingMethodKey": "pfs-international-portugal",
      "MourningShippingMethodKey": "mourning",
      "PfsShippingMethodKey": "pfs",
      "MarketingFeeToBeSettedOnProductForInternationalOutbound" : 9
    }

  SlackAlertConfig: |-
    "SlackAlert": {
      "DefaultChannel": "alerts-ms-pt-preprod",
      "BotName": "Error Alert Bot",
      "MaxRetryAttempts": 3
    }

  LegacyBackendEndpoint: |-
   "LegacyBackendEndpoint": {
      "Authentication": {
        "Credentials": {
          "grant_type": "client_credentials",
          "resource": "https://intf-test.sandbox.operations.eu.dynamics.com/",
        },
        "URL": "https://login.windows.net/interflora.es/oauth2/token",
        "AuthMethod": "OAUTH",
        "UseExpirationTime": true
      },
      "Url": "https://intf-test.sandbox.operations.eu.dynamics.com/",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  LegacyBackendEndpointList: |-
    "LegacyBackendEndpointList": {
      "OrderCheckUpdate": {
        "Action": "POST",
        "Endpoint": "/api/services/EQMPFS/EQMPFSCheckOrderStatusUpdate/checkOrderStatusUpdatePRT"
      }
    }

  Kafka: |-
    "Kafka": {
      "TopicsToCreateConfigurations": [
        {
          "TopicName": "order",
          "NumberOfPartitions": 20,
          "RetentionMs": 7200000 //2h
        }
      ]
    }

  KafkaTopicsSettings: |-
    "KafkaTopicsSettings": {
      "Order": "order"
    }

  spanishOrderSettings: |-
    "SpanishOrderSettings": {
      "ITFPLUSproductKey": "ITFPLUS",
      "VatKey": "vat",
      "VatReducedKey": "vat-reduced",
      "PfsShippingMethodKey": "pfs",
      "PfsReducedShippingMethodKey": "pfs-reduced",
      "PfsMourningReducedShippingMethodKey": "reduced-mourning",
      "ProductsHiddenToSenderFlorist": [ "PCK0001" ],
      "CityStates": [],
      "States": [
        {
          "code": "PT11",
          "key": "north",
          "name": "Norte"
        },
        {
          "code": "PT15",
          "key": "algarve",
          "name": "Algarve"
        },
        {
          "code": "PT16",
          "key": "centro",
          "name": "Centro"
        },
        {
          "code": "PT17",
          "key": "lisbon",
          "name": "Lisboa"
        },
        {
          "code": "PT18",
          "key": "alentejo",
          "name": "Alentejo"
        },
        {
          "code": "PT20",
          "key": "azores",
          "name": "Açores"
        },
        {
          "code": "PT30",
          "key": "madeira",
          "name": "Madeira"
        }
      ],
      "Provinces": [
        {
          "code": "PT111",
          "name": "Alto Minho",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT112",
          "name": "Cávado",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT119",
          "name": "Ave",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT11A",
          "name": "Porto",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT11B",
          "name": "Alto Tâmega",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT11C",
          "name": "Tâmega e Sousa",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT11D",
          "name": "Douro",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT11E",
          "name": "Terras de Trás-os-Montes",
          "stateCode": "PT11",
          "postalCodePrefixes": []
        },
        {
          "code": "PT150",
          "name": "Algarve",
          "stateCode": "PT15",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16B",
          "name": "Oeste",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16D",
          "name": "Região de Aveiro",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16E",
          "name": "Região de Coimbra",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16F",
          "name": "Região de Leiria",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16G",
          "name": "Viseu Dão-Lafões",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16H",
          "name": "Beira Baixa",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16I",
          "name": "Médio Tejo",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT16J",
          "name": "Beiras e Serra da Estrela",
          "stateCode": "PT16",
          "postalCodePrefixes": []
        },
        {
          "code": "PT170",
          "name": "Lisboa",
          "stateCode": "PT17",
          "postalCodePrefixes": ["1"]
        },
        {
          "code": "PT181",
          "name": "Alentejo Litoral",
          "stateCode": "PT18",
          "postalCodePrefixes": []
        },
        {
          "code": "PT184",
          "name": "Baixo Alentejo",
          "stateCode": "PT18",
          "postalCodePrefixes": []
        },
        {
          "code": "PT185",
          "name": "Lezíria do Tejo",
          "stateCode": "PT18",
          "postalCodePrefixes": []
        },
        {
          "code": "PT186",
          "name": "Alto Alentejo",
          "stateCode": "PT18",
          "postalCodePrefixes": []
        },
        {
          "code": "PT187",
          "name": "Alentejo Central",
          "stateCode": "PT18",
          "postalCodePrefixes": []
        },
        {
          "code": "PT200",
          "name": "Açores",
          "stateCode": "PT20",
          "postalCodePrefixes": ["95", "96", "97", "98", "99"]
        },
        {
          "code": "PT300",
          "name": "Madeira",
          "stateCode": "PT30",
          "postalCodePrefixes": ["90", "91", "92", "93", "94"]
        }
      ]
    }

  OrderConfirmationSettings: |-
    "OrderConfirmationSettings": {
      "Base64Key_256bits": "3AWwu9vquWCbGHm1xKDWLBAPPftaeob5RvyLG4pHbeY=",
      "Base64Iv_128bits": "sA9mD2QeD7h/NFwEaRK/wA=="
    }

serilogConfig: |-       
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "itp-itctorderwrapper"
    }
  }

env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "preprod"

  - name: ASPNETCORE_COUNTRY
    value: "pt"

  - name: MongoDb__DatabaseName
    value: "it-florist"
    
  - name: MongoDb__ConnectionString
    value: "mongodb://itp-ms-mongodb-0.itp-ms-mongodb-headless.itp-ms-common.svc.cluster.local:27017,itp-ms-mongodb-1.itp-ms-mongodb-headless.itp-ms-common.svc.cluster.local:27017,itp-ms-mongodb-2.itp-ms-mongodb-headless.itp-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: ElasticApm__Environment
    value: "itp-preprod"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "itp-itctorderwrapper"

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "itp-ms-kafka-headless.itp-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "itp-ms-redis-headless.itp-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://itp-unleash.itp-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "itp-itctorderwrapper"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "production"