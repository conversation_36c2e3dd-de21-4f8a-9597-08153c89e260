﻿namespace IT.Microservices.CT.Order.Wrapper.Presentation.DTO;

public class LocalizedStringValueDTO : IEquatable<LocalizedStringValueDTO>
{
    public string Locale { get; set; }
    public string Value { get; set; }

    public LocalizedStringValueDTO(string locale, string value)
    {
        Locale = locale;
        Value = value;
    }

    public bool Equals(LocalizedStringValueDTO other)
        => (other.Locale == Locale &&
        other.Value == Value);

}
