﻿using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Serialization;
using IT.Microservices.CT.Order.Wrapper.Application;
using IT.Microservices.CT.Order.Wrapper.Infrastructure;
using IT.Microservices.CT.Order.Wrapper.Infrastructure.Settings;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using ITF.SharedModels.DataModels.Globals;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Messages.Group.Order;
using JasperFx.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace IT.Microservices.CT.Order.Wrapper.Presentation;

public class InputOrderController(IOrderService orderService, ILogger<UpdateOrderController> logger, ISequenceGeneratorService sequenceGeneratorService,
    IOrderLogHistoryKafkaPublisherHelper orderLogHistoryKafkaPublisherHelper, IOptionsMonitor<KafkaTopicsSettings> kafkaTopicsSettings,
    IOptionsMonitor<CTOrderWrapperSettings> ctOrderWrapperSettings, IFloristRepository floristRepository, SerializerService serializerService, 
    IOrderValidationFacade orderValidationFacade) : BaseController
{
    [HttpPost("PostOrder")]
    public async Task<IActionResult> PostOrderAsync([FromBody] GlobalOrderModel order)
    {
        // to display deserialisation problems
        string errorMessage = string.Empty;
        if (!ModelState.IsValid)
        {               
            var errors = ModelState.Select(x => x.Value.Errors).Where(y => y.Count > 0).ToList();
            errors.ForEach(error => errorMessage += String.Join(", ", error.Select(x => x.ErrorMessage)));
            return new BadRequestObjectResult(new ErrorObjectReponse("IncorrectBodyModelException", errorMessage, logger));
        }

        if (String.IsNullOrWhiteSpace(order?.DeliveryCountryCode) && !string.IsNullOrWhiteSpace(order?.Billing?.CountryCode))
        {
            order.DeliveryCountryCode = order?.Billing?.CountryCode;
        }

        var validationResponse = await orderValidationFacade.ValidateOrder(order);

        if(!validationResponse.IsValide)
        {
            return new BadRequestObjectResult(new ErrorObjectReponse(validationResponse.ErrorType, validationResponse.ErrorMessage, logger));
        }


        if(!validationResponse.SelectedFloristId.IsEmpty())
        {
            order.ExecutingFloristIdentifier = validationResponse.SelectedFloristId;
        }

        try
        {
            await orderService.Initialize();
            await SetOrderNumber(order);

            order.Src = "PFS"; // settted to identify orders created in PFS within _orderService.HandleOrderCreated
            var result = await orderService.HandleOrderCreated(order);

            if (result != null)
            {
                var transmitterFlorist = await floristRepository.GetById(order.SenderFloristIdentifier);
                string[] countryCodesAddDocSender = new[] { "IT2" };
                if (countryCodesAddDocSender.Contains(ctOrderWrapperSettings?.CurrentValue?.CountryCode))
                {
                    await AddOrderSenderDocument(order, result, transmitterFlorist);
                }

                try
                {
                    await AddOrderHistory(order, result);
                }
                catch (Exception ex)
                {
                    logger.LogWarning($"Order {order.OrderNumber} created but publishing of the events 'ORDER NUMBER GENERATED' failed because of {ex.Message} ::: " + ex.StackTrace);
                }
            }

            return Ok(result);
        }
        catch (BadRequestException ex)
        {
            logger.LogError("Exception: " + ex.Message + ":::" + ex.StackTrace); // temporary log write
            return new BadRequestObjectResult(new ErrorObjectReponse(ex.GetType().Name, ex.Body, logger));
        }
        catch (Exception ex)
        {
            logger.LogError("Exception: " + ex.Message + ":::" + ex.StackTrace); // temporary log write
            return new BadRequestObjectResult(new ErrorObjectReponse(ex.GetType().Name, ex.Message, logger));
        }

        async Task AddOrderHistory(GlobalOrderModel order, commercetools.Sdk.Api.Models.Orders.IOrder? result)
        {
            OrderNewHistoryRecordMessageBuilder historyBuilder = new OrderNewHistoryRecordMessageBuilder();
            historyBuilder.AddCommerceToolsID(result.Id ?? "null")
                .AddOrderNumber(result.OrderNumber ?? "null")
                .AddInitialOrderStatus(result.GetFloristOrderStatus() ?? "null")
                .AddExecutingFloristId(result.GetExecutingFloristId() ?? "null")
                .AddOrderAmount(result.GetTotalItemsPrice() + result.GetDeliveryPrice())
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(order) ?? "null")
                .AddOrderAction("ORDER NUMBER GENERATED")
                .AddMessage($"Created order in CT for pfs order {result.GetLegacyOrderNumber()} with OrderNumber {order.OrderNumber} and CommerceToolsOrderID {result.Id}" ?? "null")
                .AddCtOrderPreUpdate(result?.Serialize(SerializerType.CommerceTools, serializerService) ?? "null");
            await orderLogHistoryKafkaPublisherHelper.Publish(historyBuilder.Build(), kafkaTopicsSettings.CurrentValue.Order);
        }
    }

    private async Task SetOrderNumber(GlobalOrderModel order)
    {
        if (String.IsNullOrWhiteSpace(order.OrderNumber))
        {
            long nextOrderNumber = await sequenceGeneratorService.GetNext();
            if (nextOrderNumber > 0)
            {
                if (ctOrderWrapperSettings?.CurrentValue?.FrenchSettings.OrderNumberPrefix != null)
                {
                    order.OrderNumber = ctOrderWrapperSettings.CurrentValue.FrenchSettings.OrderNumberPrefix + nextOrderNumber.ToString();
                }
                else
                {
                    order.OrderNumber = nextOrderNumber.ToString();
                }
            }
        }
    }

    private async Task AddOrderSenderDocument(GlobalOrderModel order, commercetools.Sdk.Api.Models.Orders.IOrder? result, ITF.SharedModels.DataModels.Florist.GlobalFloristModel transmitterFlorist)
    {
        string url = ctOrderWrapperSettings?.CurrentValue?.PFsGetOrderDocumentUrlFormat
            .Replace("{orderIdentifier}", result.Id)
            .Replace("{floristIdentifier}", order.SenderFloristIdentifier)
            .Replace("{type}", ((int)ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_SENDER).ToString());

        transmitterFlorist.Documents.Add(new ITF.SharedModels.DataModels.Florist.Document
        {
            CTOrderId = result.Id,
            DocType = ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_SENDER,
            FileExtension = "pdf",
            FileName = result.OrderNumber + ".pdf",
            OctopusOrderId = result.OrderNumber,
            OrderReference = result.Id,
            Month = result.CreatedAt.Month,
            Year = result.CreatedAt.Year,
            Url = url
        });

        await floristRepository.SaveDocuments(transmitterFlorist);
    }
}
