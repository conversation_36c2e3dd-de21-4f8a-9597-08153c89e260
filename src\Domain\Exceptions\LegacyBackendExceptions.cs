﻿namespace IT.Microservices.CT.Order.Wrapper.Domain.Exceptions;

public class LegacyBackendExceptions
{
    public class LegacyBackendInfrastructureException : Exception
    {
        public LegacyBackendInfrastructureException()
        {
        }
        public LegacyBackendInfrastructureException(string message) : base(message)
        {
        }
        public LegacyBackendInfrastructureException(string message, HttpResponseMessage response) : base(message)
        {
        }

        public LegacyBackendInfrastructureException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    public class InvariantException : Exception
    {
        public InvariantException()
        {
        }

        public InvariantException(string message) : base(message)
        {
        }

        public InvariantException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
