﻿
using ITF.SharedLibraries.HttpClient;
using ITF.SharedLibraries.RAO.DTO;

namespace ITF.SharedLibraries.RAO
{
    public interface IRAOSupplierHttpService : IHttpClient
    {
        Task<HttpResponseMessage> GetFloristCalendars(string floristId);
        Task<HttpResponseMessage> GetFloristCalendarException(string floristId, string start, string end);
        Task<HttpResponseMessage> AddFloristCalendarException(string floristId, string user, AddFloristCalendarExceptionRAODTO dto);
        Task<HttpResponseMessage> UpdateFloristCalendarException(string floristId, string date, string user, CalendarException dto);
        Task<HttpResponseMessage> UpdateOrderStatus(string orderId, string newStatus, UpdateOrderStatusRAODTO dto);
        Task<HttpResponseMessage> DeleteFloristCalendarException(string floristId, string date, string user, string calendarExceptionId);
        Task<HttpResponseMessage> UpdateFloristCalendar(string floristId, string dayOfWeek, string user, Calendar dto);
        Task<HttpResponseMessage> UpdateFloristCodeAP(string floristId, string codeAP, string user);
        Task<HttpResponseMessage> UpdateFloristEmail(string floristId, string email, string user);
        Task<HttpResponseMessage> UpdateFloristProductStock(string floristId, string productId, bool hasStock, string source);
        Task<HttpResponseMessage> GetFloristContext(string floristId);
        Task<HttpResponseMessage> Feasibility(FeasabilityOrderDto order, int maxFloristsInResponse);
        Task<HttpResponseMessage> InitDeliveryCourierRequest(InitDeliveryCourierRequestDto initDeliveryCourierRequestDto);
        Task<HttpResponseMessage> GetOrder(string orderNumber);
        Task<HttpResponseMessage> ResetDeliveryCourierRequest(string orderNumber, bool canRetry);
        Task<HttpResponseMessage> PatchOrderStatus(string orderId, string newStatus, string floristId, string statusComment, string source);
    }
}
